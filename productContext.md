# 产品上下文文档

## 项目存在价值
VS Code Cleaner Tool解决了开发者在使用VS Code时遇到的隐私和数据管理问题，特别是针对Augment插件相关的数据清理需求。

## 解决的问题
1. **数据隐私问题** - VS Code会收集和存储大量遥测数据，用户需要工具来管理这些数据
2. **插件残留问题** - 卸载Augment插件后，数据库中仍可能残留相关数据
3. **设备标识问题** - 用户希望能够重置或修改VS Code的设备标识信息
4. **跨平台一致性** - 需要在不同操作系统上提供统一的清理体验

## 系统工作原理
### 数据库清理流程
1. 自动检测当前操作系统类型
2. 根据系统类型确定VS Code数据库路径
3. 使用SQLite API连接数据库
4. 查询并删除包含"augment"关键词的所有条目
5. 提供操作结果反馈

### 遥测ID修改流程
1. 定位VS Code的storage.json配置文件
2. 生成新的64位机器ID和UUID设备ID
3. 更新配置文件中的相关字段
4. 确保文件格式和权限正确

### 批量操作流程
1. 按顺序执行数据库清理
2. 执行遥测ID修改
3. 提供详细的操作日志和结果反馈

## 用户体验目标
1. **简单易用** - 提供直观的命令行界面，支持单一命令完成所有操作
2. **安全可靠** - 所有操作都有详细反馈，确保用户了解执行结果
3. **高性能** - 利用MoonBit LLVM后端的优化能力，提供快速的执行体验
4. **跨平台一致** - 在不同操作系统上提供相同的用户体验
5. **零配置** - 无需额外安装依赖或配置环境

## 目标用户群体
- 使用VS Code的开发者
- 关注隐私保护的用户
- 需要清理Augment插件数据的用户
- 希望重置VS Code设备标识的用户
