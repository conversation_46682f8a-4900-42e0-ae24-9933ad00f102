# Syft在Kubernetes环境中的容器扫描指南

> **文档验证说明**: 本指南基于Anchore Syft官方文档（GitHub Wiki）验证和更新，确保命令和最佳实践的准确性。

## 1. 概述

Syft可以通过多种方式扫描Kubernetes集群中的容器镜像和运行时容器，用于生成软件物料清单(SBOM)，进行安全分析和合规检查。

**支持的源类型**（官方文档确认）:
- `docker` - Docker daemon中的镜像
- `podman` - Podman daemon中的镜像
- `containerd` - Containerd daemon中的镜像
- `registry` - 直接从Registry拉取镜像（推荐用于K8s）
- `docker-archive` - Docker存档文件
- `oci-archive` - OCI存档文件
- `oci-dir` - OCI目录格式
- `singularity` - Singularity镜像格式

## 2. 扫描方式分类

### 2.1 镜像扫描（推荐）
扫描容器镜像是最常用和最准确的方式，因为镜像包含完整的文件系统层。

### 2.2 运行时容器扫描
扫描正在运行的容器，适用于需要分析运行时状态的场景。

### 2.3 集群批量扫描
批量扫描整个Kubernetes集群中的所有容器镜像。

## 3. 基本扫描命令

### 3.1 扫描容器镜像
```bash
# 扫描本地镜像
syft <image_name>:<tag>

# 扫描远程镜像（从Registry拉取）
syft registry:<registry_url>/<image_name>:<tag>

# 扫描Docker Hub镜像
syft nginx:1.21-alpine
syft mysql:8.0
syft redis:7-alpine

# 扫描私有Registry镜像
syft registry:harbor.company.com/project/app:v1.0.0
```

### 3.2 扫描运行中的容器
```bash
# 通过容器ID扫描
syft docker:<container_id>

# 通过容器名称扫描
syft docker:<container_name>

# 扫描Podman容器
syft podman:<container_id>
```

## 4. Kubernetes特定扫描场景

### 4.1 获取Pod中的容器镜像
```bash
# 获取指定Pod的镜像信息
kubectl get pod <pod_name> -o jsonpath='{.spec.containers[*].image}'

# 获取指定命名空间所有Pod的镜像
kubectl get pods -n <namespace> -o jsonpath='{.items[*].spec.containers[*].image}' | tr ' ' '\n' | sort -u

# 获取整个集群的所有镜像
kubectl get pods --all-namespaces -o jsonpath='{.items[*].spec.containers[*].image}' | tr ' ' '\n' | sort -u
```

### 4.2 扫描Pod中的特定容器
```bash
# 1. 获取Pod信息
kubectl describe pod <pod_name> -n <namespace>

# 2. 获取容器镜像名称
kubectl get pod <pod_name> -n <namespace> -o jsonpath='{.spec.containers[0].image}'

# 3. 扫描该镜像
syft $(kubectl get pod <pod_name> -n <namespace> -o jsonpath='{.spec.containers[0].image}')
```

### 4.3 扫描Deployment中的镜像
```bash
# 获取Deployment使用的镜像
kubectl get deployment <deployment_name> -o jsonpath='{.spec.template.spec.containers[*].image}'

# 扫描Deployment的镜像
syft $(kubectl get deployment nginx-deployment -o jsonpath='{.spec.template.spec.containers[0].image}')
```

## 5. 批量扫描脚本

### 5.1 扫描命名空间中所有镜像
```bash
#!/bin/bash
# scan_namespace.sh - 扫描指定命名空间的所有容器镜像

NAMESPACE=${1:-default}
OUTPUT_DIR="./sbom_reports"

echo "扫描命名空间: $NAMESPACE"
mkdir -p $OUTPUT_DIR

# 获取命名空间中所有唯一镜像
IMAGES=$(kubectl get pods -n $NAMESPACE -o jsonpath='{.items[*].spec.containers[*].image}' | tr ' ' '\n' | sort -u)

for IMAGE in $IMAGES; do
    echo "正在扫描镜像: $IMAGE"
    
    # 生成安全的文件名
    SAFE_NAME=$(echo $IMAGE | sed 's/[\/:]/_/g')
    
    # 扫描并生成SBOM
    syft $IMAGE -o syft-json > "$OUTPUT_DIR/${SAFE_NAME}_sbom.json"
    syft $IMAGE -o spdx-json > "$OUTPUT_DIR/${SAFE_NAME}_spdx.json"
    
    echo "完成扫描: $IMAGE"
done

echo "所有镜像扫描完成，报告保存在: $OUTPUT_DIR"
```

### 5.2 扫描整个集群
```bash
#!/bin/bash
# scan_cluster.sh - 扫描整个Kubernetes集群的所有镜像

OUTPUT_DIR="./cluster_sbom"
REPORT_FILE="cluster_scan_report.txt"

echo "开始扫描Kubernetes集群..." | tee $REPORT_FILE
mkdir -p $OUTPUT_DIR

# 获取所有命名空间
NAMESPACES=$(kubectl get namespaces -o jsonpath='{.items[*].metadata.name}')

for NS in $NAMESPACES; do
    echo "扫描命名空间: $NS" | tee -a $REPORT_FILE
    
    # 获取该命名空间的所有镜像
    IMAGES=$(kubectl get pods -n $NS -o jsonpath='{.items[*].spec.containers[*].image}' 2>/dev/null | tr ' ' '\n' | sort -u)
    
    if [ -z "$IMAGES" ]; then
        echo "  命名空间 $NS 中没有运行的Pod" | tee -a $REPORT_FILE
        continue
    fi
    
    for IMAGE in $IMAGES; do
        echo "  扫描镜像: $IMAGE" | tee -a $REPORT_FILE
        
        SAFE_NAME=$(echo "${NS}_${IMAGE}" | sed 's/[\/:]/_/g')
        
        # 扫描镜像
        if syft $IMAGE -o syft-json > "$OUTPUT_DIR/${SAFE_NAME}_sbom.json" 2>/dev/null; then
            echo "    ✓ 扫描成功" | tee -a $REPORT_FILE
        else
            echo "    ✗ 扫描失败" | tee -a $REPORT_FILE
        fi
    done
done

echo "集群扫描完成！" | tee -a $REPORT_FILE
```

## 6. 高级扫描选项

### 6.1 指定输出格式
```bash
# JSON格式（默认）
syft nginx:alpine -o syft-json

# SPDX格式
syft nginx:alpine -o spdx-json

# CycloneDX格式
syft nginx:alpine -o cyclonedx-json

# 表格格式（人类可读）
syft nginx:alpine -o syft-table

# 文本格式
syft nginx:alpine -o syft-text
```

### 6.2 认证和私有Registry
```bash
# 使用Docker配置的认证信息（推荐方式）
syft registry:private-registry.com/app:latest

# 使用环境变量指定Docker配置路径
export DOCKER_CONFIG=/path/to/docker/config
syft registry:private-registry.com/app:latest

# 在容器中使用认证
docker run -v ~/.docker/config.json:/config/config.json -e "DOCKER_CONFIG=/config" anchore/syft:latest registry:private-registry.com/app:latest
```

**注意：** 根据官方文档，Syft主要通过Docker配置文件（~/.docker/config.json）进行认证，而不是命令行参数。

### 6.3 指定源类型（--from参数）
根据官方文档，Syft支持以下源类型：
```bash
# 明确指定源类型
syft --from docker nginx:alpine          # Docker daemon
syft --from podman nginx:alpine          # Podman daemon
syft --from containerd nginx:alpine      # Containerd daemon
syft --from registry nginx:alpine        # 直接从Registry拉取
syft --from docker-archive image.tar     # Docker存档文件
syft --from oci-archive image.tar        # OCI存档文件
syft --from oci-dir ./image-dir          # OCI目录
syft --from singularity image.sif        # Singularity镜像
syft --from dir ./directory              # 目录扫描
syft --from file ./single-file           # 单文件扫描
```

### 6.4 扫描配置选项
```bash
# 详细输出
syft nginx:alpine -v

# 静默模式
syft nginx:alpine -q

# 指定平台架构
syft nginx:alpine --platform linux/amd64

# 包含所有层（不仅仅是最终镜像）
syft nginx:alpine --scope all-layers

# 排除特定路径
syft nginx:alpine --exclude '/tmp/**'
```

## 7. Kubernetes中的私有Registry认证

### 7.1 创建认证Secret
```bash
# 创建包含Docker配置的Secret
kubectl create secret generic registry-config \
  --from-file=config.json=~/.docker/config.json \
  -n syft
```

### 7.2 在Pod中使用认证
```yaml
# pod.yaml - 在Kubernetes中运行Syft扫描私有镜像
apiVersion: v1
kind: Pod
metadata:
  name: syft-k8s-usage
  namespace: syft
spec:
  containers:
    - image: anchore/syft:latest
      name: syft-private-registry-demo
      env:
        - name: DOCKER_CONFIG
          value: /config
      volumeMounts:
      - mountPath: /config
        name: registry-config
        readOnly: true
      args:
        - registry:private-registry.com/app:latest
  volumes:
  - name: registry-config
    secret:
      secretName: registry-config
  restartPolicy: Never
```

### 7.3 查看扫描结果
```bash
# 应用Pod配置
kubectl apply -f pod.yaml

# 查看扫描日志
kubectl logs syft-k8s-usage -n syft

# 清理资源
kubectl delete pod syft-k8s-usage -n syft
```

## 8. 与Kubernetes工具集成

### 8.1 与kubectl结合使用
```bash
# 一键扫描当前Pod的第一个容器
kubectl get pod <pod_name> -o jsonpath='{.spec.containers[0].image}' | xargs syft

# 扫描所有nginx Pod的镜像
kubectl get pods -l app=nginx -o jsonpath='{.items[*].spec.containers[*].image}' | tr ' ' '\n' | sort -u | xargs -I {} syft {}
```

### 7.2 与Helm结合
```bash
# 获取Helm Release使用的镜像
helm get manifest <release_name> | grep 'image:' | awk '{print $2}' | sort -u

# 扫描Helm Chart中的镜像
helm template <chart_name> | grep 'image:' | awk '{print $2}' | sort -u | xargs -I {} syft {}
```

## 8. CI/CD集成示例

### 8.1 GitLab CI配置
```yaml
# .gitlab-ci.yml
stages:
  - security-scan

sbom-scan:
  stage: security-scan
  image: anchore/syft:latest
  script:
    - |
      # 获取部署的镜像列表
      kubectl get deployment -o jsonpath='{.items[*].spec.template.spec.containers[*].image}' > images.txt
      
      # 扫描每个镜像
      while read image; do
        echo "Scanning $image"
        syft $image -o spdx-json > "sbom_$(echo $image | sed 's/[\/:]/_/g').json"
      done < images.txt
  artifacts:
    paths:
      - "sbom_*.json"
    expire_in: 30 days
```

### 8.2 GitHub Actions配置
```yaml
# .github/workflows/k8s-sbom-scan.yml
name: Kubernetes SBOM Scan

on:
  push:
    branches: [ main ]
  schedule:
    - cron: '0 2 * * 1'  # 每周一凌晨2点

jobs:
  sbom-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Install Syft
      run: |
        curl -sSfL https://get.anchore.io/syft | sudo sh -s -- -b /usr/local/bin
    
    - name: Configure kubectl
      uses: azure/k8s-set-context@v1
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG }}
    
    - name: Scan Kubernetes Images
      run: |
        mkdir -p sbom-reports
        kubectl get pods --all-namespaces -o jsonpath='{.items[*].spec.containers[*].image}' | \
        tr ' ' '\n' | sort -u | while read image; do
          echo "Scanning $image"
          safe_name=$(echo $image | sed 's/[\/:]/_/g')
          syft $image -o spdx-json > "sbom-reports/${safe_name}.json"
        done
    
    - name: Upload SBOM Reports
      uses: actions/upload-artifact@v3
      with:
        name: sbom-reports
        path: sbom-reports/
```

## 9. 最佳实践

### 9.1 扫描策略
1. **定期扫描**: 建立定期扫描机制，监控新的漏洞
2. **镜像优先**: 优先扫描镜像而非运行时容器
3. **版本固定**: 使用具体版本标签，避免latest标签
4. **分层扫描**: 区分基础镜像和应用镜像的扫描

### 9.2 性能优化（基于官方建议）

#### 9.2.1 一次性扫描大镜像
```bash
# 使用registry源绕过Docker daemon开销
syft --from registry myimage:latest
```
**优势**: 避免Docker daemon的tar准备步骤，对大镜像扫描更快。

#### 9.2.2 多次扫描同一镜像
```bash
# 1. 预先保存镜像
docker image save -o image.tar myimage:latest

# 2. 扫描tar文件（避免重复的daemon操作）
syft ./image.tar
```

#### 9.2.3 使用Skopeo优化
```bash
# 使用skopeo创建OCI格式
skopeo copy docker://myimage:latest oci:./my-image

# 扫描OCI目录
syft --from oci-dir ./my-image
```

#### 9.2.4 其他性能建议
1. **并行扫描**: 使用并行处理提高扫描效率
2. **缓存结果**: 缓存相同镜像的扫描结果
3. **增量扫描**: 只扫描变更的镜像
4. **资源限制**: 在CI/CD中设置合理的资源限制

### 9.3 安全建议
1. **权限控制**: 限制扫描工具的集群访问权限
2. **数据保护**: 保护SBOM数据，避免泄露敏感信息
3. **合规检查**: 结合漏洞数据库进行合规性检查
4. **告警机制**: 建立高危漏洞的告警机制

## 10. 故障排除

### 10.1 常见问题
```bash
# 镜像拉取失败
syft nginx:alpine --registry-insecure-skip-tls-verify

# 认证问题
docker login <registry>
syft <registry>/<image>

# 网络问题
syft <image> --registry-timeout 300s

# 权限问题
sudo syft docker:<container_id>
```

### 10.2 调试命令
```bash
# 详细日志
syft <image> -vv

# 检查镜像信息
docker inspect <image>

# 验证镜像可访问性
docker pull <image>
```

通过以上方法，可以有效地在Kubernetes环境中使用Syft进行容器安全扫描和SBOM生成。
