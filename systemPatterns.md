# 系统模式文档

## 系统架构设计

### 整体架构
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   CLI Interface │────│  Core Logic      │────│  Native Libs    │
│   (cli.mbt)     │    │  (vscode_cleaner │    │  (C Libraries)  │
│                 │    │   system_utils)  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         v                       v                       v
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Main Entry    │    │  FFI Bindings    │    │  System APIs    │
│   (main.mbt)    │    │ (native_bindings)│    │ (SQLite, UUID)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 分层设计
1. **表示层** - CLI命令解析和用户交互
2. **业务层** - VS Code清理逻辑和系统工具
3. **数据层** - FFI绑定和原生库调用
4. **系统层** - C库实现和系统API包装

## 关键技术决策

### 语言选择
- **MoonBit主语言**：提供类型安全和高性能
- **C语言FFI层**：处理系统API和SQLite操作
- **LLVM编译后端**：生成优化的原生机器码

### 模块划分策略
1. **main.mbt** - 程序入口点，处理命令行参数
2. **cli.mbt** - 命令行界面逻辑，参数解析
3. **vscode_cleaner.mbt** - 核心清理逻辑实现
4. **system_utils.mbt** - 系统工具函数集合
5. **native_bindings.mbt** - FFI函数声明和类型定义

### 错误处理模式
```moonbit
// 使用Result类型进行错误传播
enum Result[T, E] {
  Ok(T)
  Err(E)
}

// 统一的错误类型定义
enum CleanerError {
  DatabaseError(String)
  FileSystemError(String)
  ConfigError(String)
}
```

## 采用的设计模式

### 1. 命令模式 (Command Pattern)
每个CLI命令对应一个具体的操作函数：
- `clean` → `clean_vscode_database()`
- `modify-ids` → `modify_telemetry_ids()`
- `all` → `execute_all_operations()`

### 2. 策略模式 (Strategy Pattern)
根据操作系统类型选择不同的路径策略：
- Windows路径策略
- macOS路径策略  
- Linux路径策略

### 3. 外观模式 (Facade Pattern)
通过FFI绑定层隐藏C库的复杂性，提供简洁的MoonBit接口

### 4. 工厂模式 (Factory Pattern)
根据系统类型创建相应的路径处理器和配置管理器

## 组件交互关系

### 数据流向
1. **用户输入** → CLI解析器 → 命令分发器
2. **命令执行** → 业务逻辑层 → FFI调用层
3. **系统操作** → C库函数 → 系统API
4. **结果返回** → 错误处理 → 用户反馈

### 依赖关系
- main.mbt 依赖 cli.mbt
- cli.mbt 依赖 vscode_cleaner.mbt, system_utils.mbt
- vscode_cleaner.mbt 依赖 native_bindings.mbt
- native_bindings.mbt 依赖 C库 (libvscode_cleaner.so)

## 核心实现路径

### 数据库清理路径
1. 检测操作系统类型
2. 构建VS Code数据库路径
3. 建立SQLite连接
4. 执行清理SQL语句
5. 关闭连接并返回结果

### 配置修改路径
1. 定位storage.json文件
2. 读取现有配置内容
3. 生成新的ID值
4. 更新配置并写回文件
5. 验证操作结果

### FFI调用路径
1. MoonBit函数调用
2. 类型转换和参数准备
3. C函数调用
4. 结果处理和类型转换
5. 错误检查和异常处理

## 性能优化策略
1. **编译时优化** - 利用LLVM的优化pass
2. **内存管理** - 依赖MoonBit的GC，避免手动管理
3. **I/O优化** - 批量数据库操作，减少系统调用
4. **缓存策略** - 缓存路径检测结果，避免重复计算
