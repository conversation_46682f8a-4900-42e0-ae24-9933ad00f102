# 技术上下文文档

## 使用的技术栈

### 主要编程语言
- **MoonBit** - 主要开发语言，提供类型安全和高性能
- **C语言** - FFI层实现，系统API包装

### 编译工具链
- **MoonBit编译器** - 源码编译和类型检查
- **LLVM后端** - 代码优化和机器码生成
- **GCC** - C库编译和链接

### 核心依赖库
- **SQLite3** - 数据库操作 (`libsqlite3-dev`)
- **UUID库** - 唯一标识符生成 (`libuuid-dev`)
- **pthread** - 线程支持
- **标准C库** - 基础系统调用

## 开发环境配置

### 系统要求
- Linux操作系统 (开发环境)
- MoonBit工具链已安装
- GCC编译器 (版本7.0+)
- Make构建工具

### 环境变量设置
```bash
# MoonBit工具链路径
export PATH="$HOME/.moon/bin:$PATH"

# 库文件搜索路径
export LD_LIBRARY_PATH="./native_libs:$LD_LIBRARY_PATH"

# MoonBit运行时库路径
export MOONBIT_RUNTIME_PATH="/root/.moon"
```

### 开发工具
- **文本编辑器** - VS Code (带MoonBit插件)
- **构建工具** - Make (仅用于C库), 手动命令行构建
- **调试工具** - GDB (用于C库调试)
- **版本控制** - Git

## 技术限制条件

### MoonBit语言限制
- FFI调用需要明确的类型声明
- 内存管理依赖GC，无法精确控制
- 标准库功能相对有限，需要C库补充

### 跨平台限制
- 路径分隔符差异 (Windows: `\`, Unix: `/`)
- VS Code安装路径因系统而异
- 系统API调用方式不同

### 性能限制
- FFI调用存在一定开销
- 字符串处理需要类型转换
- 文件I/O操作受系统限制

## 依赖项清单

### MoonBit项目依赖 (moon.mod.json)
```json
{
  "link": {
    "native": {
      "flags": [
        "-lpthread",
        "-L./native_libs", 
        "-lvscode_cleaner",
        "-lsqlite3",
        "-luuid"
      ]
    }
  }
}
```

### C库依赖 (Makefile)
- **sqlite3** - 数据库操作接口
- **uuid** - UUID生成功能
- **pthread** - 线程支持库
- **标准C库** - 基础系统功能

### 系统包依赖
```bash
# Ubuntu/Debian
sudo apt-get install libsqlite3-dev libuuid-dev build-essential

# CentOS/RHEL
sudo yum install sqlite-devel libuuid-devel gcc make

# macOS
brew install sqlite ossp-uuid
```

## 工具使用规范

### 构建流程
**自动化构建（推荐）**：
```bash
# 使用自动化构建脚本
./build.sh
```

**手动构建步骤**：
```bash
# 1. 构建C库
cd native_libs && make clean && make && cd ..

# 2. 构建MoonBit项目（会失败但生成中间文件）
moon build --target native || true

# 3. 手动链接生成可执行文件
/usr/bin/cc -o target/native/release/build/main/main.exe \
    -I/root/.moon/include -L/root/.moon/lib \
    -fwrapv -fno-strict-aliasing -O2 \
    /root/.moon/lib/libmoonbitrun.o \
    target/native/release/build/main/main.c \
    target/native/release/build/runtime.o \
    -lm -L./native_libs -lvscode_cleaner -lsqlite3 -luuid -lpthread
```

**链接问题解决方案**：
- MoonBit的链接标志配置无法正确传递给最终链接步骤
- 解决方法：让MoonBit生成中间文件，然后手动执行链接
- 关键：pthread库必须放在链接命令的最后位置

### 代码规范
- **命名约定** - 使用snake_case命名函数和变量
- **模块组织** - 每个功能模块独立文件
- **错误处理** - 统一使用Result类型
- **注释规范** - 关键函数必须有文档注释

### 部署规范
- **可执行文件** - 生成独立的原生二进制文件
- **库文件** - 确保共享库在正确路径
- **权限设置** - 可执行文件需要适当权限
- **路径配置** - 设置LD_LIBRARY_PATH环境变量
