<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.Date" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>JSP Health Check Service</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 600px; margin: 0 auto; }
        .health-status { background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; }
        .endpoint { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; border-radius: 3px; margin: 10px 0; }
        code { background: #e9ecef; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>JSP Health Check Service</h1>
        
        <div class="health-status">
            <h3>Service Status: <span style="color: green;">RUNNING</span></h3>
            <p><strong>Current Time:</strong> <%= new Date() %></p>
            <p><strong>Server Info:</strong> <%= application.getServerInfo() %></p>
            <p><strong>Java Version:</strong> <%= System.getProperty("java.version") %></p>
        </div>
        
        <h3>Available Endpoints:</h3>
        
        <div class="endpoint">
            <h4>Health Check API</h4>
            <p><strong>URL:</strong> <code>/health</code></p>
            <p><strong>Method:</strong> GET</p>
            <p><strong>Response:</strong> JSON format health status</p>
            <p><a href="health" target="_blank">Test Health Endpoint</a></p>
        </div>
        
        <h3>Sample Usage:</h3>
        <div class="endpoint">
            <code>curl -X GET http://localhost:8080/jsp-health/health</code>
        </div>
        
        <h3>Project Information:</h3>
        <ul>
            <li>Framework: Java Servlet/JSP</li>
            <li>Java Version: Compatible with Java 1.8+</li>
            <li>Deployment: Standard WAR file</li>
            <li>Dependencies: Minimal (Servlet API only)</li>
        </ul>
    </div>
</body>
</html>
