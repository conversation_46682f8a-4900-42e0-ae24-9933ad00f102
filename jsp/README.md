# JSP Health Check Service

A simple JSP-based web application that provides a RESTful health check endpoint.

## Project Structure

```
jsp/
├── src/
│   └── main/
│       ├── java/
│       │   └── com/
│       │       └── example/
│       │           └── HealthCheckServlet.java
│       └── webapp/
│           ├── index.jsp
│           └── WEB-INF/
│               └── web.xml
├── lib/
├── build.sh
└── README.md
```

## Features

- **Health Check Endpoint**: `/health` - Returns JSON status information
- **Welcome Page**: `/` - Shows service information and available endpoints
- **Minimal Dependencies**: Only requires Servlet API
- **Java 8 Compatible**: Uses older Java syntax for compatibility

## API Endpoints

### GET /health

Returns the health status of the service in JSON format.

**Response Example:**
```json
{
  "status": "UP",
  "timestamp": "Mon Aug 18 11:52:00 CST 2025",
  "service": "JSP Health Check Service",
  "version": "1.0.0",
  "uptime": "1755487920000"
}
```

## Building and Deployment

### Prerequisites

- Java 8 or higher
- Servlet container (Tomcat, Jetty, etc.)
- servlet-api.jar (provided by container)

### Build

```bash
chmod +x build.sh
./build.sh
```

### Deployment

1. Copy the built WAR contents to your servlet container's webapps directory
2. Start the servlet container
3. Access the application at `http://localhost:8080/jsp-health/`

## Technology Stack

- **Java**: 1.8+
- **Servlet API**: 2.5
- **JSP**: 2.1
- **Build**: Shell script (no Maven/Gradle dependency)

## Testing

- Access main page: `http://localhost:8080/jsp-health/`
- Test health endpoint: `http://localhost:8080/jsp-health/health`
- Command line test: `curl http://localhost:8080/jsp-health/health`
