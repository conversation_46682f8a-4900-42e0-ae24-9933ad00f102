#!/bin/bash

# Simple JSP Project Build Script
# This script compiles the Java sources and creates a WAR file

PROJECT_NAME="jsp-health"
SRC_DIR="src/main/java"
WEB_DIR="src/main/webapp"
BUILD_DIR="build"
CLASSES_DIR="$BUILD_DIR/WEB-INF/classes"
WAR_FILE="$PROJECT_NAME.war"

echo "Building JSP Health Check Project..."

# Create build directories
mkdir -p $CLASSES_DIR
mkdir -p $BUILD_DIR/WEB-INF/lib

# Note: This script requires servlet-api.jar to be present in lib/ directory
# For demonstration purposes, we'll create a placeholder
if [ ! -f "lib/servlet-api.jar" ]; then
    echo "Warning: servlet-api.jar not found in lib/ directory"
    echo "This is a demonstration build script."
    echo "In a real environment, you would need:"
    echo "1. servlet-api.jar (from Tomcat or other servlet container)"
    echo "2. jsp-api.jar (for JSP compilation)"
    
    # Create a placeholder file to show structure
    touch lib/servlet-api.jar
fi

# Copy web resources
echo "Copying web resources..."
cp -r $WEB_DIR/* $BUILD_DIR/

# Copy libraries
if [ -d "lib" ] && [ "$(ls -A lib)" ]; then
    echo "Copying libraries..."
    cp lib/*.jar $BUILD_DIR/WEB-INF/lib/
fi

# Compile Java sources (commented out as we don't have servlet-api.jar)
echo "Java compilation would happen here with proper classpath..."
echo "javac -cp lib/servlet-api.jar -d $CLASSES_DIR $SRC_DIR/com/example/*.java"

# Create WAR file structure for demonstration
echo "Creating WAR file structure..."
cd $BUILD_DIR
find . -name "*.class" -o -name "*.jsp" -o -name "*.xml" -o -name "*.jar" > ../file-list.txt
echo "WAR file would contain:"
cat ../file-list.txt

cd ..
echo "Build completed. WAR structure created in $BUILD_DIR/"
echo "To deploy: copy contents to your servlet container's webapps directory"
