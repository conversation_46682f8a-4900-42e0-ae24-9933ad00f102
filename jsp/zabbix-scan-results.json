{"artifacts": [], "artifactRelationships": [], "source": {"id": "caf1c06dcf802728c4cfc24d645e1e7386ba0289e03ea93be21e2ad625b5a3d0", "name": "zabbix/", "version": "", "type": "directory", "metadata": {"path": "zabbix/"}}, "distro": {}, "descriptor": {"name": "syft", "version": "1.31.0", "configuration": {"catalogers": {"requested": {"default": ["directory", "file"]}, "used": ["alpm-db-cataloger", "apk-db-cataloger", "binary-classifier-cataloger", "cargo-auditable-binary-cataloger", "cocoapods-cataloger", "conan-cataloger", "dart-pubspec-cataloger", "dart-pubspec-lock-cataloger", "deb-archive-cataloger", "dotnet-deps-binary-cataloger", "dotnet-packages-lock-cataloger", "dpkg-db-cataloger", "elf-binary-package-cataloger", "elixir-mix-lock-cataloger", "erlang-otp-application-cataloger", "erlang-rebar-lock-cataloger", "file-content-cataloger", "file-digest-cataloger", "file-executable-cataloger", "file-metadata-cataloger", "github-action-workflow-usage-cataloger", "github-actions-usage-cataloger", "go-module-binary-cataloger", "go-module-file-cataloger", "graalvm-native-image-cataloger", "haskell-cataloger", "homebrew-cataloger", "java-archive-cataloger", "java-gradle-lockfile-cataloger", "java-jvm-cataloger", "java-pom-cataloger", "javascript-lock-cataloger", "linux-kernel-cataloger", "lua-rock-cataloger", "nix-cataloger", "opam-cataloger", "pe-binary-package-cataloger", "php-composer-lock-cataloger", "php-interpreter-cataloger", "php-pear-serialized-cataloger", "portage-cataloger", "python-installed-package-cataloger", "python-package-cataloger", "r-package-cataloger", "rpm-archive-cataloger", "rpm-db-cataloger", "ruby-gemfile-cataloger", "ruby-gemspec-cataloger", "rust-cargo-lock-cataloger", "swift-package-manager-cataloger", "swipl-pack-cataloger", "terraform-lock-cataloger", "wordpress-plugins-cataloger"]}, "data-generation": {"generate-cpes": true}, "files": {"content": {"globs": null, "skip-files-above-size": 0}, "hashers": ["sha-1", "sha-256"], "selection": "owned-by-package"}, "licenses": {"coverage": 75, "include-content": "none"}, "packages": {"binary": ["python-binary", "python-binary-lib", "pypy-binary-lib", "go-binary", "julia-binary", "helm", "redis-binary", "nodejs-binary", "go-binary-hint", "busybox-binary", "util-linux-binary", "haproxy-binary", "perl-binary", "php-composer-binary", "httpd-binary", "memcached-binary", "traefik-binary", "arangodb-binary", "postgresql-binary", "mysql-binary", "mysql-binary", "mysql-binary", "xtrabackup-binary", "mariadb-binary", "rust-standard-library-linux", "rust-standard-library-macos", "ruby-binary", "erlang-binary", "erlang-alpine-binary", "erlang-library", "swipl-binary", "dart-binary", "haskell-ghc-binary", "haskell-cabal-binary", "haskell-stack-binary", "consul-binary", "hashicorp-vault-binary", "nginx-binary", "bash-binary", "openssl-binary", "gcc-binary", "fluent-bit-binary", "wordpress-cli-binary", "curl-binary", "lighttpd-binary", "proftpd-binary", "zstd-binary", "xz-binary", "gzip-binary", "sqlcipher-binary", "jq-binary", "chrome-binary", "java-binary", "java-jdb-binary"], "dotnet": {"dep-packages-must-claim-dll": true, "dep-packages-must-have-dll": false, "propagate-dll-claims-to-parents": true, "relax-dll-claims-when-bundling-detected": true}, "golang": {"local-mod-cache-dir": "/root/go/pkg/mod", "local-vendor-dir": "", "main-module-version": {"from-build-settings": true, "from-contents": false, "from-ld-flags": true}, "proxies": ["https://proxy.golang.org", "direct"], "search-local-mod-cache-licenses": false, "search-local-vendor-licenses": false, "search-remote-licenses": false}, "java-archive": {"include-indexed-archives": true, "include-unindexed-archives": false, "maven-base-url": "https://repo1.maven.org/maven2", "maven-localrepository-dir": "/root/.m2/repository", "max-parent-recursive-depth": 0, "resolve-transitive-dependencies": false, "use-maven-localrepository": false, "use-network": false}, "javascript": {"include-dev-dependencies": false, "npm-base-url": "https://registry.npmjs.org", "search-remote-licenses": false}, "linux-kernel": {"catalog-modules": true}, "nix": {"capture-owned-files": false}, "python": {"guess-unpinned-requirements": false}}, "relationships": {"exclude-binary-packages-with-file-ownership-overlap": true, "package-file-ownership": true, "package-file-ownership-overlap": true}, "search": {"scope": "squashed"}}}, "schema": {"version": "16.0.37", "url": "https://raw.githubusercontent.com/anchore/syft/main/schema/json/schema-16.0.37.json"}}