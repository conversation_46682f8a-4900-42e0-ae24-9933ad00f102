{"artifacts": [{"id": "a00383392aa9ea47", "name": "at<PERSON><PERSON><PERSON>", "version": "2.0.5.RELEASE", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/attoparser-2.0.5.RELEASE.jar", "accessPath": "/target/dependency/attoparser-2.0.5.RELEASE.jar", "annotations": {"evidence": "primary"}}], "licenses": [{"value": "http://www.apache.org/licenses/LICENSE-2.0.txt", "spdxExpression": "", "type": "declared", "urls": [], "locations": [{"path": "/target/dependency/attoparser-2.0.5.RELEASE.jar", "accessPath": "/target/dependency/attoparser-2.0.5.RELEASE.jar", "annotations": {"evidence": "primary"}}]}], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:attoparser-team:attoparser:2.0.5.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:attoparser_team:attoparser:2.0.5.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:org.attoparser:attoparser:2.0.5.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:attoparser:attoparser:2.0.5.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/org.attoparser/<EMAIL>", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/attoparser-2.0.5.RELEASE.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Created-By", "value": "Apache Maven B<PERSON>le Plugin"}, {"key": "Built-By", "value": "releases"}, {"key": "Build-Jdk", "value": "9.0.4"}, {"key": "Specification-Title", "value": "at<PERSON><PERSON><PERSON>"}, {"key": "Specification-Version", "value": "2.0.5.RELEASE"}, {"key": "Specification-<PERSON><PERSON><PERSON>", "value": "The ATTOPARSER team"}, {"key": "Implementation-Title", "value": "at<PERSON><PERSON><PERSON>"}, {"key": "Implementation-Version", "value": "2.0.5.RELEASE"}, {"key": "Implementation-Vendor-Id", "value": "org.attoparser"}, {"key": "Implementation-Vendor", "value": "The ATTOPARSER team"}, {"key": "Implementation-URL", "value": "http://www.attoparser.org"}, {"key": "Automatic-Module-Name", "value": "at<PERSON><PERSON><PERSON>"}, {"key": "X-Compile-Source-JDK", "value": "6"}, {"key": "X-Compile-Target-JDK", "value": "6"}, {"key": "Bnd-LastModified", "value": "1522402327180"}, {"key": "Bundle-Description", "value": "Powerful, fast and easy to use HTML and XML parserfor Java"}, {"key": "Bundle-DocURL", "value": "http://www.attoparser.org"}, {"key": "Bundle-License", "value": "http://www.apache.org/licenses/LICENSE-2.0.txt"}, {"key": "Bundle-ManifestVersion", "value": "2"}, {"key": "Bundle-Name", "value": "at<PERSON><PERSON><PERSON>"}, {"key": "Bundle-SymbolicName", "value": "org.attoparser"}, {"key": "Bundle-Vendor", "value": "The ATTOPARSER team"}, {"key": "Bundle-Version", "value": "2.0.5.RELEASE"}, {"key": "Export-Package", "value": "org.attoparser;uses:=\"org.attoparser.config,org.attoparser.select\";version=\"2.0.5\",org.attoparser.config;version=\"2.0.5\",org.attoparser.discard;uses:=\"org.attoparser\";version=\"2.0.5\",org.attoparser.dom;uses:=\"org.attoparser,org.attoparser.config\";version=\"2.0.5\",org.attoparser.duplicate;uses:=\"org.attoparser,org.attoparser.config,org.attoparser.select\";version=\"2.0.5\",org.attoparser.minimize;uses:=\"org.attoparser,org.attoparser.config\";version=\"2.0.5\",org.attoparser.output;uses:=\"org.attoparser\";version=\"2.0.5\",org.attoparser.prettyhtml;uses:=\"org.attoparser\";version=\"2.0.5\",org.attoparser.select;uses:=\"org.attoparser,org.attoparser.config\";version=\"2.0.5\",org.attoparser.simple;uses:=\"org.attoparser,org.attoparser.config\";version=\"2.0.5\",org.attoparser.trace;uses:=\"org.attoparser\";version=\"2.0.5\",org.attoparser.util;version=\"2.0.5\",org.attoparser.dom.impl;version=\"2.0.5\""}, {"key": "Import-Package", "value": "org.attoparser,org.attoparser.config,org.attoparser.discard,org.attoparser.select,org.attoparser.util"}, {"key": "Require-Capability", "value": "osgi.ee;filter:=\"(&(osgi.ee=JavaSE)(version=1.6))\""}, {"key": "Tool", "value": "Bnd-3.5.0.201709291849"}]}, "digest": [{"algorithm": "sha1", "value": "a93ad36df9560de3a5312c1d14f69d938099fa64"}]}}, {"id": "09ee2d790acd922d", "name": "commons-lang3", "version": "3.9", "type": "java-archive", "foundBy": "java-pom-cataloger", "locations": [{"path": "/pom.xml", "accessPath": "/pom.xml", "annotations": {"evidence": "primary"}}], "licenses": [], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:apache:commons-lang3:3.9:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:apache:commons_lang3:3.9:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:apache:commons:3.9:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/org.apache.commons/commons-lang3@3.9", "metadataType": "java-archive", "metadata": {"virtualPath": "", "pomProperties": {"path": "", "name": "", "groupId": "org.apache.commons", "artifactId": "commons-lang3", "version": ""}}}, {"id": "ed580897c4ca2eb5", "name": "commons-lang3", "version": "3.9", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/commons-lang3-3.9.jar", "accessPath": "/target/dependency/commons-lang3-3.9.jar", "annotations": {"evidence": "primary"}}], "licenses": [{"value": "https://www.apache.org/licenses/LICENSE-2.0.txt", "spdxExpression": "Apache-2.0", "type": "declared", "urls": [], "locations": [{"path": "/target/dependency/commons-lang3-3.9.jar", "accessPath": "/target/dependency/commons-lang3-3.9.jar", "annotations": {"evidence": "primary"}}]}], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:apache:commons-lang3:3.9:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:apache:commons_lang3:3.9:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:apache:commons:3.9:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:apache:lang3:3.9:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/org.apache.commons/commons-lang3@3.9", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/commons-lang3-3.9.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Created-By", "value": "Apache Maven B<PERSON>le Plugin"}, {"key": "Built-By", "value": "ch<PERSON><PERSON><PERSON>"}, {"key": "Build-Jdk", "value": "11.0.2"}, {"key": "Specification-Title", "value": "Apache Commons Lang"}, {"key": "Specification-Version", "value": "3.9"}, {"key": "Specification-<PERSON><PERSON><PERSON>", "value": "The Apache Software Foundation"}, {"key": "Implementation-Title", "value": "Apache Commons Lang"}, {"key": "Implementation-Version", "value": "3.9"}, {"key": "Implementation-Vendor-Id", "value": "org.apache.commons"}, {"key": "Implementation-Vendor", "value": "The Apache Software Foundation"}, {"key": "Implementation-URL", "value": "http://commons.apache.org/proper/commons-lang/"}, {"key": "Automatic-Module-Name", "value": "org.apache.commons.lang3"}, {"key": "Bnd-LastModified", "value": "1554946229157"}, {"key": "Bundle-Description", "value": "Apache Commons Lang, a package of Java utility classes for the  classes that are in java.lang's hierarchy, or are considered to be so  standard as to justify existence in java.lang."}, {"key": "Bundle-DocURL", "value": "http://commons.apache.org/proper/commons-lang/"}, {"key": "Bundle-License", "value": "https://www.apache.org/licenses/LICENSE-2.0.txt"}, {"key": "Bundle-ManifestVersion", "value": "2"}, {"key": "Bundle-Name", "value": "Apache Commons Lang"}, {"key": "Bundle-SymbolicName", "value": "org.apache.commons.lang3"}, {"key": "Bundle-Vendor", "value": "The Apache Software Foundation"}, {"key": "Bundle-Version", "value": "3.9.0"}, {"key": "Export-Package", "value": "org.apache.commons.lang3;version=\"3.9\",org.apache.commons.lang3.arch;version=\"3.9\",org.apache.commons.lang3.builder;version=\"3.9\",org.apache.commons.lang3.concurrent;version=\"3.9\",org.apache.commons.lang3.event;version=\"3.9\",org.apache.commons.lang3.exception;version=\"3.9\",org.apache.commons.lang3.math;version=\"3.9\",org.apache.commons.lang3.mutable;version=\"3.9\",org.apache.commons.lang3.reflect;version=\"3.9\",org.apache.commons.lang3.text;version=\"3.9\",org.apache.commons.lang3.text.translate;version=\"3.9\",org.apache.commons.lang3.time;version=\"3.9\",org.apache.commons.lang3.tuple;version=\"3.9\""}, {"key": "Include-Resource", "value": "META-INF/NOTICE.txt=NOTICE.txt,META-INF/LICENSE.txt=LICENSE.txt"}, {"key": "Require-Capability", "value": "osgi.ee;filter:=\"(&(osgi.ee=JavaSE)(version=1.8))\""}, {"key": "Tool", "value": "Bnd-4.1.0.201810181252"}]}, "pomProperties": {"path": "META-INF/maven/org.apache.commons/commons-lang3/pom.properties", "name": "", "groupId": "org.apache.commons", "artifactId": "commons-lang3", "version": "3.9"}, "digest": [{"algorithm": "sha1", "value": "0122c7cee69b53ed4a7681c03d4ee4c0e2765da5"}]}}, {"id": "70e7d1ed520e8a9d", "name": "gson", "version": "2.8.6", "type": "java-archive", "foundBy": "java-pom-cataloger", "locations": [{"path": "/pom.xml", "accessPath": "/pom.xml", "annotations": {"evidence": "primary"}}], "licenses": [], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:com.google.code.gson:gson:2.8.6:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:google:gson:2.8.6:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:code:gson:2.8.6:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:gson:gson:2.8.6:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/com.google.code.gson/gson@2.8.6", "metadataType": "java-archive", "metadata": {"virtualPath": "", "pomProperties": {"path": "", "name": "", "groupId": "com.google.code.gson", "artifactId": "gson", "version": ""}}}, {"id": "cb58912cd5a6c980", "name": "gson", "version": "2.8.6", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/gson-2.8.6.jar", "accessPath": "/target/dependency/gson-2.8.6.jar", "annotations": {"evidence": "primary"}}], "licenses": [], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:com.google.code.gson:gson:2.8.6:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.google.gson:gson:2.8.6:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:google:gson:2.8.6:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:code:gson:2.8.6:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:gson:gson:2.8.6:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/com.google.code.gson/gson@2.8.6", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/gson-2.8.6.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Archiver-Version", "value": "Plexus Archiver"}, {"key": "Created-By", "value": "11.0.4 (Oracle Corporation)"}, {"key": "Built-By", "value": "inder"}, {"key": "Build-Jdk", "value": "11.0.4"}, {"key": "Bnd-LastModified", "value": "1570215293550"}, {"key": "Bundle-ContactAddress", "value": "https://github.com/google/gson"}, {"key": "Bundle-Description", "value": "Gson JSON library"}, {"key": "Bundle-ManifestVersion", "value": "2"}, {"key": "Bundle-Name", "value": "Gson"}, {"key": "Bundle-RequiredExecutionEnvironment", "value": "J2SE-1.5, JavaSE-1.6, JavaSE-1.7, JavaSE-1.8"}, {"key": "Bundle-SymbolicName", "value": "com.google.gson"}, {"key": "Bundle-Vendor", "value": "Google Gson Project"}, {"key": "Bundle-Version", "value": "2.8.6"}, {"key": "Export-Package", "value": "com.google.gson;uses:=\"com.google.gson.reflect,com.google.gson.stream\";version=\"2.8.6\",com.google.gson.annotations;version=\"2.8.6\",com.google.gson.reflect;version=\"2.8.6\",com.google.gson.stream;version=\"2.8.6\""}, {"key": "Import-Package", "value": "com.google.gson.annotations"}, {"key": "Require-Capability", "value": "osgi.ee;filter:=\"(&(osgi.ee=JavaSE)(version=9.0))\""}, {"key": "Tool", "value": "Bnd-4.0.0.201805111645"}]}, "pomProperties": {"path": "META-INF/maven/com.google.code.gson/gson/pom.properties", "name": "", "groupId": "com.google.code.gson", "artifactId": "gson", "version": "2.8.6"}, "digest": [{"algorithm": "sha1", "value": "9180733b7df8542621dc12e21e87557e8c99b8cb"}]}}, {"id": "afb0085e06d1bb0b", "name": "hamcrest-core", "version": "1.3", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/hamcrest-core-1.3.jar", "accessPath": "/target/dependency/hamcrest-core-1.3.jar", "annotations": {"evidence": "primary"}}], "licenses": [{"value": "BSD-3-<PERSON><PERSON>", "spdxExpression": "BSD-3-<PERSON><PERSON>", "type": "concluded", "urls": [], "locations": [{"path": "/target/dependency/hamcrest-core-1.3.jar", "accessPath": "/target/dependency/hamcrest-core-1.3.jar", "annotations": {"evidence": "primary"}}]}], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:hamcrest-core:hamcrest-core:1.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:hamcrest-core:hamcrest_core:1.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:hamcrest_core:hamcrest-core:1.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:hamcrest_core:hamcrest_core:1.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:hamcrest-org:hamcrest-core:1.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:hamcrest-org:hamcrest_core:1.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:hamcrest_org:hamcrest-core:1.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:hamcrest_org:hamcrest_core:1.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:hamcrest:hamcrest-core:1.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:hamcrest:hamcrest_core:1.3:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/hamcrest-core/hamcrest-core@1.3", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/hamcrest-core-1.3.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Ant-Version", "value": "Apache Ant 1.8.1"}, {"key": "Created-By", "value": "1.6.0_33-b03 (Sun Microsystems Inc.)"}, {"key": "Implementation-Title", "value": "hamcrest-core"}, {"key": "Implementation-Vendor", "value": "hamcrest.org"}, {"key": "Implementation-Version", "value": "1.3"}, {"key": "Built-By", "value": "tom"}, {"key": "Built-Date", "value": "2012-07-09 19:49:34"}]}, "digest": [{"algorithm": "sha1", "value": "42a25dc3219429f0e5d060061f71acb49bf010a0"}]}}, {"id": "ae5877e5630935d9", "name": "jackson-annotations", "version": "2.9.10", "type": "java-archive", "foundBy": "java-pom-cataloger", "locations": [{"path": "/pom.xml", "accessPath": "/pom.xml", "annotations": {"evidence": "primary"}}], "licenses": [], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:jackson-annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:jackson_annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-annotations:jackson-annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-annotations:jackson_annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_annotations:jackson-annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_annotations:jackson_annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:jackson-annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:jackson_annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-annotations:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:jackson-annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:jackson_annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_annotations:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:jackson-annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:jackson_annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/com.fasterxml.jackson.core/jackson-annotations@2.9.10", "metadataType": "java-archive", "metadata": {"virtualPath": "", "pomProperties": {"path": "", "name": "", "groupId": "com.fasterxml.jackson.core", "artifactId": "jackson-annotations", "version": ""}}}, {"id": "eb228c50d7df59b7", "name": "jackson-annotations", "version": "2.9.10", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/jackson-annotations-2.9.10.jar", "accessPath": "/target/dependency/jackson-annotations-2.9.10.jar", "annotations": {"evidence": "primary"}}], "licenses": [{"value": "http://www.apache.org/licenses/LICENSE-2.0.txt", "spdxExpression": "", "type": "declared", "urls": [], "locations": [{"path": "/target/dependency/jackson-annotations-2.9.10.jar", "accessPath": "/target/dependency/jackson-annotations-2.9.10.jar", "annotations": {"evidence": "primary"}}]}], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:com.fasterxml.jackson.core.jackson-annotations:jackson-annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core.jackson-annotations:jackson_annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core.jackson-annotations:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:jackson-annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:jackson_annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-annotations:jackson-annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-annotations:jackson_annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_annotations:jackson-annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_annotations:jackson_annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:jackson-annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:jackson_annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-annotations:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:jackson-annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:jackson_annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_annotations:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:jackson-annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:jackson_annotations:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/com.fasterxml.jackson.core/jackson-annotations@2.9.10", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/jackson-annotations-2.9.10.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Automatic-Module-Name", "value": "com.fasterxml.jackson.annotation"}, {"key": "Bnd-LastModified", "value": "1569087426657"}, {"key": "Build-Jdk", "value": "1.8.0_162"}, {"key": "Built-By", "value": "tatu"}, {"key": "Bundle-Description", "value": "Core annotations used for value types, used by Jackson data binding package."}, {"key": "Bundle-DocURL", "value": "http://github.com/FasterXML/jackson"}, {"key": "Bundle-License", "value": "http://www.apache.org/licenses/LICENSE-2.0.txt"}, {"key": "Bundle-ManifestVersion", "value": "2"}, {"key": "Bundle-Name", "value": "Jackson-annotations"}, {"key": "Bundle-SymbolicName", "value": "com.fasterxml.jackson.core.jackson-annotations"}, {"key": "Bundle-Vendor", "value": "FasterXML"}, {"key": "Bundle-Version", "value": "2.9.10"}, {"key": "Created-By", "value": "Apache Maven B<PERSON>le Plugin"}, {"key": "Export-Package", "value": "com.fasterxml.jackson.annotation;version=\"2.9.10\""}, {"key": "Implementation-Build-Date", "value": "2019-09-21 17:36:55+0000"}, {"key": "Implementation-Title", "value": "Jackson-annotations"}, {"key": "Implementation-Vendor", "value": "FasterXML"}, {"key": "Implementation-Vendor-Id", "value": "com.fasterxml.jackson.core"}, {"key": "Implementation-Version", "value": "2.9.10"}, {"key": "Require-Capability", "value": "osgi.ee;filter:=\"(&(osgi.ee=JavaSE)(version=1.6))\""}, {"key": "Specification-Title", "value": "Jackson-annotations"}, {"key": "Specification-<PERSON><PERSON><PERSON>", "value": "FasterXML"}, {"key": "Specification-Version", "value": "2.9.10"}, {"key": "Tool", "value": "Bnd-2.3.0.201405100607"}, {"key": "X-Compile-Source-JDK", "value": "1.6"}, {"key": "X-Compile-Target-JDK", "value": "1.6"}]}, "pomProperties": {"path": "META-INF/maven/com.fasterxml.jackson.core/jackson-annotations/pom.properties", "name": "", "groupId": "com.fasterxml.jackson.core", "artifactId": "jackson-annotations", "version": "2.9.10"}, "digest": [{"algorithm": "sha1", "value": "53ab2f0f92e87ea4874c8c6997335c211d81e636"}]}}, {"id": "eec6db8941e5b28e", "name": "jackson-core", "version": "2.9.10", "type": "java-archive", "foundBy": "java-pom-cataloger", "locations": [{"path": "/pom.xml", "accessPath": "/pom.xml", "annotations": {"evidence": "primary"}}], "licenses": [], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:jackson-core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:jackson_core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-core:jackson-core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-core:jackson_core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_core:jackson-core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_core:jackson_core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:jackson-core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:jackson_core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-core:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:jackson-core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:jackson_core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_core:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:jackson-core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:jackson_core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-core:core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_core:core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/com.fasterxml.jackson.core/jackson-core@2.9.10", "metadataType": "java-archive", "metadata": {"virtualPath": "", "pomProperties": {"path": "", "name": "", "groupId": "com.fasterxml.jackson.core", "artifactId": "jackson-core", "version": ""}}}, {"id": "aeda88acf8f42219", "name": "jackson-core", "version": "2.9.10", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/jackson-core-2.9.10.jar", "accessPath": "/target/dependency/jackson-core-2.9.10.jar", "annotations": {"evidence": "primary"}}], "licenses": [{"value": "http://www.apache.org/licenses/LICENSE-2.0.txt", "spdxExpression": "", "type": "declared", "urls": [], "locations": [{"path": "/target/dependency/jackson-core-2.9.10.jar", "accessPath": "/target/dependency/jackson-core-2.9.10.jar", "annotations": {"evidence": "primary"}}]}], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:com.fasterxml.jackson.core.jackson-core:jackson-core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core.jackson-core:jackson_core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core.jackson-core:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core.jackson-core:core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:jackson-core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:jackson_core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-core:jackson-core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-core:jackson_core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_core:jackson-core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_core:jackson_core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:jackson-core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:jackson_core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-core:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:jackson-core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:jackson_core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_core:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:jackson-core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:jackson_core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-core:core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_core:core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:core:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/com.fasterxml.jackson.core/jackson-core@2.9.10", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/jackson-core-2.9.10.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Automatic-Module-Name", "value": "com.fasterxml.jackson.core"}, {"key": "Bnd-LastModified", "value": "1569088236219"}, {"key": "Build-Jdk", "value": "1.8.0_162"}, {"key": "Built-By", "value": "tatu"}, {"key": "Bundle-Description", "value": "Core Jackson processing abstractions (aka StreamingAPI), implementation for JSON"}, {"key": "Bundle-DocURL", "value": "https://github.com/FasterXML/jackson-core"}, {"key": "Bundle-License", "value": "http://www.apache.org/licenses/LICENSE-2.0.txt"}, {"key": "Bundle-ManifestVersion", "value": "2"}, {"key": "Bundle-Name", "value": "<PERSON>-<PERSON>"}, {"key": "Bundle-SymbolicName", "value": "com.fasterxml.jackson.core.jackson-core"}, {"key": "Bundle-Vendor", "value": "FasterXML"}, {"key": "Bundle-Version", "value": "2.9.10"}, {"key": "Created-By", "value": "Apache Maven B<PERSON>le Plugin"}, {"key": "Export-Package", "value": "com.fasterxml.jackson.core;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.core.async,com.fasterxml.jackson.core.format,com.fasterxml.jackson.core.io,com.fasterxml.jackson.core.sym,com.fasterxml.jackson.core.type,com.fasterxml.jackson.core.util\",com.fasterxml.jackson.core.async;version=\"2.9.10\",com.fasterxml.jackson.core.base;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.core,com.fasterxml.jackson.core.io,com.fasterxml.jackson.core.json,com.fasterxml.jackson.core.util\",com.fasterxml.jackson.core.filter;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.core,com.fasterxml.jackson.core.util\",com.fasterxml.jackson.core.format;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.core\",com.fasterxml.jackson.core.io;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.core,com.fasterxml.jackson.core.util\",com.fasterxml.jackson.core.json;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.core,com.fasterxml.jackson.core.base,com.fasterxml.jackson.core.format,com.fasterxml.jackson.core.io,com.fasterxml.jackson.core.sym\",com.fasterxml.jackson.core.json.async;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.core,com.fasterxml.jackson.core.async,com.fasterxml.jackson.core.base,com.fasterxml.jackson.core.io,com.fasterxml.jackson.core.sym\",com.fasterxml.jackson.core.sym;version=\"2.9.10\",com.fasterxml.jackson.core.type;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.core\",com.fasterxml.jackson.core.util;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.core,com.fasterxml.jackson.core.io\""}, {"key": "Implementation-Build-Date", "value": "2019-09-21 17:50:13+0000"}, {"key": "Implementation-Title", "value": "<PERSON>-<PERSON>"}, {"key": "Implementation-Vendor", "value": "FasterXML"}, {"key": "Implementation-Vendor-Id", "value": "com.fasterxml.jackson.core"}, {"key": "Implementation-Version", "value": "2.9.10"}, {"key": "Require-Capability", "value": "osgi.ee;filter:=\"(&(osgi.ee=JavaSE)(version=1.6))\""}, {"key": "Specification-Title", "value": "<PERSON>-<PERSON>"}, {"key": "Specification-<PERSON><PERSON><PERSON>", "value": "FasterXML"}, {"key": "Specification-Version", "value": "2.9.10"}, {"key": "Tool", "value": "Bnd-2.3.0.201405100607"}, {"key": "X-Compile-Source-JDK", "value": "1.6"}, {"key": "X-Compile-Target-JDK", "value": "1.6"}]}, "pomProperties": {"path": "META-INF/maven/com.fasterxml.jackson.core/jackson-core/pom.properties", "name": "", "groupId": "com.fasterxml.jackson.core", "artifactId": "jackson-core", "version": "2.9.10"}, "digest": [{"algorithm": "sha1", "value": "66b715dec9dd8b0f39f3296e67e05913bf422d0c"}]}}, {"id": "bed203887412f6b5", "name": "jackson-databind", "version": "2.9.10", "type": "java-archive", "foundBy": "java-pom-cataloger", "locations": [{"path": "/pom.xml", "accessPath": "/pom.xml", "annotations": {"evidence": "primary"}}], "licenses": [], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:jackson-databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:jackson_databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-databind:jackson-databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-databind:jackson_databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_databind:jackson-databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_databind:jackson_databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:jackson-databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:jackson_databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-databind:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:jackson-databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:jackson_databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_databind:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:jackson-databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:jackson_databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/com.fasterxml.jackson.core/jackson-databind@2.9.10", "metadataType": "java-archive", "metadata": {"virtualPath": "", "pomProperties": {"path": "", "name": "", "groupId": "com.fasterxml.jackson.core", "artifactId": "jackson-databind", "version": ""}}}, {"id": "4fcb6ead1ed74d0a", "name": "jackson-databind", "version": "2.9.10", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/jackson-databind-2.9.10.jar", "accessPath": "/target/dependency/jackson-databind-2.9.10.jar", "annotations": {"evidence": "primary"}}], "licenses": [{"value": "http://www.apache.org/licenses/LICENSE-2.0.txt", "spdxExpression": "", "type": "declared", "urls": [], "locations": [{"path": "/target/dependency/jackson-databind-2.9.10.jar", "accessPath": "/target/dependency/jackson-databind-2.9.10.jar", "annotations": {"evidence": "primary"}}]}], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:com.fasterxml.jackson.core.jackson-databind:jackson-databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core.jackson-databind:jackson_databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core.jackson-databind:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:jackson-databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:jackson_databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.fasterxml.jackson.core:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-databind:jackson-databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-databind:jackson_databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_databind:jackson-databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_databind:jackson_databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:jackson-databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:jackson_databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson-databind:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:jackson-databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:jackson_databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson_databind:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:jackson-databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:jackson_databind:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:fasterxml:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jackson:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:core:jackson:2.9.10:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/com.fasterxml.jackson.core/jackson-databind@2.9.10", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/jackson-databind-2.9.10.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Automatic-Module-Name", "value": "com.fasterxml.jackson.databind"}, {"key": "Bnd-LastModified", "value": "1569088796411"}, {"key": "Build-Jdk", "value": "1.8.0_162"}, {"key": "Built-By", "value": "tatu"}, {"key": "Bundle-Description", "value": "General data-binding functionality for Jackson: works on core streaming API"}, {"key": "Bundle-DocURL", "value": "http://github.com/FasterXML/jackson"}, {"key": "Bundle-License", "value": "http://www.apache.org/licenses/LICENSE-2.0.txt"}, {"key": "Bundle-ManifestVersion", "value": "2"}, {"key": "Bundle-Name", "value": "jackson-databind"}, {"key": "Bundle-SymbolicName", "value": "com.fasterxml.jackson.core.jackson-databind"}, {"key": "Bundle-Vendor", "value": "FasterXML"}, {"key": "Bundle-Version", "value": "2.9.10"}, {"key": "Created-By", "value": "Apache Maven B<PERSON>le Plugin"}, {"key": "Export-Package", "value": "com.fasterxml.jackson.databind;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.annotation,com.fasterxml.jackson.core,com.fasterxml.jackson.core.filter,com.fasterxml.jackson.core.format,com.fasterxml.jackson.core.io,com.fasterxml.jackson.core.type,com.fasterxml.jackson.databind.annotation,com.fasterxml.jackson.databind.cfg,com.fasterxml.jackson.databind.deser,com.fasterxml.jackson.databind.deser.impl,com.fasterxml.jackson.databind.introspect,com.fasterxml.jackson.databind.jsonFormatVisitors,com.fasterxml.jackson.databind.jsonschema,com.fasterxml.jackson.databind.jsontype,com.fasterxml.jackson.databind.jsontype.impl,com.fasterxml.jackson.databind.node,com.fasterxml.jackson.databind.ser,com.fasterxml.jackson.databind.ser.impl,com.fasterxml.jackson.databind.type,com.fasterxml.jackson.databind.util\",com.fasterxml.jackson.databind.annotation;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.annotation,com.fasterxml.jackson.databind,com.fasterxml.jackson.databind.deser,com.fasterxml.jackson.databind.jsontype,com.fasterxml.jackson.databind.ser,com.fasterxml.jackson.databind.util\",com.fasterxml.jackson.databind.cfg;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.annotation,com.fasterxml.jackson.core,com.fasterxml.jackson.core.type,com.fasterxml.jackson.databind,com.fasterxml.jackson.databind.deser,com.fasterxml.jackson.databind.introspect,com.fasterxml.jackson.databind.jsontype,com.fasterxml.jackson.databind.ser,com.fasterxml.jackson.databind.type,com.fasterxml.jackson.databind.util\",com.fasterxml.jackson.databind.deser;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.annotation,com.fasterxml.jackson.core,com.fasterxml.jackson.core.format,com.fasterxml.jackson.databind,com.fasterxml.jackson.databind.annotation,com.fasterxml.jackson.databind.cfg,com.fasterxml.jackson.databind.deser.impl,com.fasterxml.jackson.databind.deser.std,com.fasterxml.jackson.databind.introspect,com.fasterxml.jackson.databind.jsonFormatVisitors,com.fasterxml.jackson.databind.jsontype,com.fasterxml.jackson.databind.type,com.fasterxml.jackson.databind.util\",com.fasterxml.jackson.databind.deser.impl;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.annotation,com.fasterxml.jackson.core,com.fasterxml.jackson.databind,com.fasterxml.jackson.databind.cfg,com.fasterxml.jackson.databind.deser,com.fasterxml.jackson.databind.deser.std,com.fasterxml.jackson.databind.introspect,com.fasterxml.jackson.databind.jsontype,com.fasterxml.jackson.databind.util\",com.fasterxml.jackson.databind.deser.std;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.annotation,com.fasterxml.jackson.core,com.fasterxml.jackson.databind,com.fasterxml.jackson.databind.annotation,com.fasterxml.jackson.databind.deser,com.fasterxml.jackson.databind.deser.impl,com.fasterxml.jackson.databind.introspect,com.fasterxml.jackson.databind.jsontype,com.fasterxml.jackson.databind.util\",com.fasterxml.jackson.databind.exc;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.core,com.fasterxml.jackson.databind,com.fasterxml.jackson.databind.introspect\",com.fasterxml.jackson.databind.ext;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.core,com.fasterxml.jackson.databind,com.fasterxml.jackson.databind.deser,com.fasterxml.jackson.databind.deser.std,com.fasterxml.jackson.databind.introspect,com.fasterxml.jackson.databind.jsonFormatVisitors,com.fasterxml.jackson.databind.jsontype,com.fasterxml.jackson.databind.ser,com.fasterxml.jackson.databind.ser.std,javax.xml.datatype,javax.xml.parsers,org.w3c.dom,org.w3c.dom.ls\",com.fasterxml.jackson.databind.introspect;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.annotation,com.fasterxml.jackson.core,com.fasterxml.jackson.databind,com.fasterxml.jackson.databind.annotation,com.fasterxml.jackson.databind.cfg,com.fasterxml.jackson.databind.jsontype,com.fasterxml.jackson.databind.jsontype.impl,com.fasterxml.jackson.databind.ser,com.fasterxml.jackson.databind.type,com.fasterxml.jackson.databind.util\",com.fasterxml.jackson.databind.jsonFormatVisitors;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.annotation,com.fasterxml.jackson.core,com.fasterxml.jackson.databind\",com.fasterxml.jackson.databind.jsonschema;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.annotation,com.fasterxml.jackson.databind,com.fasterxml.jackson.databind.node\",com.fasterxml.jackson.databind.jsontype;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.annotation,com.fasterxml.jackson.core,com.fasterxml.jackson.core.type,com.fasterxml.jackson.databind,com.fasterxml.jackson.databind.cfg,com.fasterxml.jackson.databind.introspect\",com.fasterxml.jackson.databind.jsontype.impl;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.annotation,com.fasterxml.jackson.core,com.fasterxml.jackson.core.type,com.fasterxml.jackson.databind,com.fasterxml.jackson.databind.cfg,com.fasterxml.jackson.databind.introspect,com.fasterxml.jackson.databind.jsontype,com.fasterxml.jackson.databind.type,com.fasterxml.jackson.databind.util\",com.fasterxml.jackson.databind.module;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.core,com.fasterxml.jackson.databind,com.fasterxml.jackson.databind.deser,com.fasterxml.jackson.databind.jsontype,com.fasterxml.jackson.databind.ser,com.fasterxml.jackson.databind.type\",com.fasterxml.jackson.databind.node;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.core,com.fasterxml.jackson.core.base,com.fasterxml.jackson.databind,com.fasterxml.jackson.databind.jsontype,com.fasterxml.jackson.databind.util\",com.fasterxml.jackson.databind.ser;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.annotation,com.fasterxml.jackson.core,com.fasterxml.jackson.core.io,com.fasterxml.jackson.databind,com.fasterxml.jackson.databind.annotation,com.fasterxml.jackson.databind.cfg,com.fasterxml.jackson.databind.introspect,com.fasterxml.jackson.databind.jsonFormatVisitors,com.fasterxml.jackson.databind.jsonschema,com.fasterxml.jackson.databind.jsontype,com.fasterxml.jackson.databind.node,com.fasterxml.jackson.databind.ser.impl,com.fasterxml.jackson.databind.ser.std,com.fasterxml.jackson.databind.type,com.fasterxml.jackson.databind.util\",com.fasterxml.jackson.databind.ser.impl;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.annotation,com.fasterxml.jackson.core,com.fasterxml.jackson.core.io,com.fasterxml.jackson.databind,com.fasterxml.jackson.databind.annotation,com.fasterxml.jackson.databind.cfg,com.fasterxml.jackson.databind.introspect,com.fasterxml.jackson.databind.jsonFormatVisitors,com.fasterxml.jackson.databind.jsontype,com.fasterxml.jackson.databind.node,com.fasterxml.jackson.databind.ser,com.fasterxml.jackson.databind.ser.std,com.fasterxml.jackson.databind.util\",com.fasterxml.jackson.databind.ser.std;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.annotation,com.fasterxml.jackson.core,com.fasterxml.jackson.core.type,com.fasterxml.jackson.databind,com.fasterxml.jackson.databind.annotation,com.fasterxml.jackson.databind.introspect,com.fasterxml.jackson.databind.jsonFormatVisitors,com.fasterxml.jackson.databind.jsonschema,com.fasterxml.jackson.databind.jsontype,com.fasterxml.jackson.databind.node,com.fasterxml.jackson.databind.ser,com.fasterxml.jackson.databind.ser.impl,com.fasterxml.jackson.databind.type,com.fasterxml.jackson.databind.util\",com.fasterxml.jackson.databind.type;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.core,com.fasterxml.jackson.core.type,com.fasterxml.jackson.databind,com.fasterxml.jackson.databind.jsontype,com.fasterxml.jackson.databind.util\",com.fasterxml.jackson.databind.util;version=\"2.9.10\";uses:=\"com.fasterxml.jackson.annotation,com.fasterxml.jackson.core,com.fasterxml.jackson.core.base,com.fasterxml.jackson.core.json,com.fasterxml.jackson.core.util,com.fasterxml.jackson.databind,com.fasterxml.jackson.databind.cfg,com.fasterxml.jackson.databind.deser,com.fasterxml.jackson.databind.introspect,com.fasterxml.jackson.databind.jsontype,com.fasterxml.jackson.databind.type\""}, {"key": "Implementation-Build-Date", "value": "2019-09-21 17:59:40+0000"}, {"key": "Implementation-Title", "value": "jackson-databind"}, {"key": "Implementation-Vendor", "value": "FasterXML"}, {"key": "Implementation-Vendor-Id", "value": "com.fasterxml.jackson.core"}, {"key": "Implementation-Version", "value": "2.9.10"}, {"key": "Import-Package", "value": "org.w3c.dom.bootstrap;resolution:=optional,com.fasterxml.jackson.annotation;version=\"[2.9,3)\",com.fasterxml.jackson.core;version=\"[2.9,3)\",com.fasterxml.jackson.core.base;version=\"[2.9,3)\",com.fasterxml.jackson.core.filter;version=\"[2.9,3)\",com.fasterxml.jackson.core.format;version=\"[2.9,3)\",com.fasterxml.jackson.core.io;version=\"[2.9,3)\",com.fasterxml.jackson.core.json;version=\"[2.9,3)\",com.fasterxml.jackson.core.type;version=\"[2.9,3)\",com.fasterxml.jackson.core.util;version=\"[2.9,3)\",com.fasterxml.jackson.databind;version=\"[2.9,3)\",com.fasterxml.jackson.databind.annotation;version=\"[2.9,3)\",com.fasterxml.jackson.databind.cfg;version=\"[2.9,3)\",com.fasterxml.jackson.databind.deser;version=\"[2.9,3)\",com.fasterxml.jackson.databind.deser.impl;version=\"[2.9,3)\",com.fasterxml.jackson.databind.deser.std;version=\"[2.9,3)\",com.fasterxml.jackson.databind.exc;version=\"[2.9,3)\",com.fasterxml.jackson.databind.ext;version=\"[2.9,3)\",com.fasterxml.jackson.databind.introspect;version=\"[2.9,3)\",com.fasterxml.jackson.databind.jsonFormatVisitors;version=\"[2.9,3)\",com.fasterxml.jackson.databind.jsonschema;version=\"[2.9,3)\",com.fasterxml.jackson.databind.jsontype;version=\"[2.9,3)\",com.fasterxml.jackson.databind.jsontype.impl;version=\"[2.9,3)\",com.fasterxml.jackson.databind.node;version=\"[2.9,3)\",com.fasterxml.jackson.databind.ser;version=\"[2.9,3)\",com.fasterxml.jackson.databind.ser.impl;version=\"[2.9,3)\",com.fasterxml.jackson.databind.ser.std;version=\"[2.9,3)\",com.fasterxml.jackson.databind.type;version=\"[2.9,3)\",com.fasterxml.jackson.databind.util;version=\"[2.9,3)\",javax.xml.datatype,javax.xml.namespace,javax.xml.parsers,org.w3c.dom,org.w3c.dom.ls,org.xml.sax"}, {"key": "Require-Capability", "value": "osgi.ee;filter:=\"(&(osgi.ee=JavaSE)(version=1.7))\""}, {"key": "Specification-Title", "value": "jackson-databind"}, {"key": "Specification-<PERSON><PERSON><PERSON>", "value": "FasterXML"}, {"key": "Specification-Version", "value": "2.9.10"}, {"key": "Tool", "value": "Bnd-3.2.0.201605172007"}, {"key": "X-Compile-Source-JDK", "value": "1.7"}, {"key": "X-Compile-Target-JDK", "value": "1.7"}]}, "pomProperties": {"path": "META-INF/maven/com.fasterxml.jackson.core/jackson-databind/pom.properties", "name": "", "groupId": "com.fasterxml.jackson.core", "artifactId": "jackson-databind", "version": "2.9.10"}, "digest": [{"algorithm": "sha1", "value": "e201bb70b7469ba18dd58ed8268aa44e702fa2f0"}]}}, {"id": "f9c94850d7f61da8", "name": "javassist", "version": "3.20.0-GA", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/javassist-3.20.0-GA.jar", "accessPath": "/target/dependency/javassist-3.20.0-GA.jar", "annotations": {"evidence": "primary"}}], "licenses": [{"value": "http://www.mozilla.org/MPL/MPL-1.1.html, http://www.gnu.org/licenses/lgpl-2.1.html, http://www.apache.org/licenses/", "spdxExpression": "", "type": "declared", "urls": [], "locations": [{"path": "/target/dependency/javassist-3.20.0-GA.jar", "accessPath": "/target/dependency/javassist-3.20.0-GA.jar", "annotations": {"evidence": "primary"}}]}], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:shigeru-chiba\\,-www-javassist-org:javassist:3.20.0-GA:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:shigeru_chiba\\,_www_javassist_org:javassist:3.20.0-GA:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:org.javassist:javassist:3.20.0-GA:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javassist:javassist:3.20.0-GA:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/org.javassist/javassist@3.20.0-GA", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/javassist-3.20.0-GA.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Bundle-Description", "value": "Javassist (JAVA programming ASSISTant) makes Javabytecode manipulation    simple.  It is a class library for editing bytecodes in Java."}, {"key": "Bundle-License", "value": "http://www.mozilla.org/MPL/MPL-1.1.html, http://www.gnu.org/licenses/lgpl-2.1.html, http://www.apache.org/licenses/"}, {"key": "Bundle-SymbolicName", "value": "javassist"}, {"key": "Built-By", "value": "smarlow"}, {"key": "Bundle-ManifestVersion", "value": "2"}, {"key": "Bnd-LastModified", "value": "1435234169221"}, {"key": "Specification-<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>, www.javassist.org"}, {"key": "Specification-Title", "value": "Javassist"}, {"key": "Bundle-Vendor", "value": "<PERSON><PERSON><PERSON>, www.javassist.org"}, {"key": "Tool", "value": "Bnd-0.0.357"}, {"key": "Main-Class", "value": "javassist.CtClass"}, {"key": "Export-Package", "value": "javassist.convert;uses:=\"javassist.bytecode.analysis,javassist.bytecode,javassist\";version=\"3.20.0.GA\",javassist.tools;uses:=\"javassist,javassist.bytecode,javassist.bytecode.analysis\";version=\"3.20.0.GA\",javassist.tools.reflect;uses:=\"javassist,javassist.bytecode\";version=\"3.20.0.GA\",javassist.expr;uses:=\"javassist.compiler,javassist.bytecode,javassist.compiler.ast,javassist\";version=\"3.20.0.GA\",javassist.tools.rmi;uses:=\"javassist.tools.web,javassist\";version=\"3.20.0.GA\",javassist.util;version=\"3.20.0.GA\",javassist.bytecode.analysis;uses:=\"javassist.bytecode,javassist,javassist.bytecode.stackmap\";version=\"3.20.0.GA\",javassist.bytecode.annotation;uses:=\"javassist.bytecode,javassist\";version=\"3.20.0.GA\",javassist.bytecode.stackmap;uses:=\"javassist.bytecode,javassist\";version=\"3.20.0.GA\",javassist.util.proxy;uses:=\"javassist.bytecode,javassist\";version=\"3.20.0.GA\",javassist.compiler;uses:=\"javassist.bytecode,javassist,javassist.compiler.ast\";version=\"3.20.0.GA\",javassist.runtime;version=\"3.20.0.GA\",javassist.bytecode;uses:=\"javassist.bytecode.annotation,javassist,javassist.bytecode.stackmap\";version=\"3.20.0.GA\",javassist.scopedpool;uses:=javassist;version=\"3.20.0.GA\",javassist.compiler.ast;uses:=\"javassist.compiler,javassist\";version=\"3.20.0.GA\",javassist;uses:=\"javassist.compiler,javassist.bytecode,javassist.convert,javassist.expr,javassist.bytecode.annotation,javassist.compiler.ast\";version=\"3.20.0.GA\",javassist.tools.web;uses:=javassist;version=\"3.20.0.GA\""}, {"key": "Bundle-Version", "value": "3.20.0.GA"}, {"key": "Bundle-Name", "value": "Javassist"}, {"key": "Ignore-Package", "value": "com.sun.jdi.event,com.sun.jdi,com.sun.jdi.request,com.sun.jdi.connect"}, {"key": "Created-By", "value": "Apache Maven B<PERSON>le Plugin"}, {"key": "Build-Jdk", "value": "1.8.0_45"}, {"key": "Specification-Version", "value": "3.20.0-GA"}]}, "pomProperties": {"path": "META-INF/maven/org.javassist/javassist/pom.properties", "name": "", "groupId": "org.javassist", "artifactId": "javassist", "version": "3.20.0-GA"}, "digest": [{"algorithm": "sha1", "value": "a9cbcdfb7e9f86fbc74d3afae65f2248bfbf82a0"}]}}, {"id": "62674903d1839d81", "name": "javax.servlet-api", "version": "3.1.0", "type": "java-archive", "foundBy": "java-pom-cataloger", "locations": [{"path": "/pom.xml", "accessPath": "/pom.xml", "annotations": {"evidence": "primary"}}], "licenses": [], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:javax.servlet-api:javax.servlet-api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet-api:javax.servlet_api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet_api:javax.servlet-api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet_api:javax.servlet_api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet:javax.servlet-api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet:javax.servlet_api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/javax.servlet/javax.servlet-api@3.1.0", "metadataType": "java-archive", "metadata": {"virtualPath": "", "pomProperties": {"path": "", "name": "", "groupId": "javax.servlet", "artifactId": "javax.servlet-api", "version": "", "scope": "provided"}}}, {"id": "a36cb0d400bcb22b", "name": "javax.servlet-api", "version": "3.1.0", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/javax.servlet-api-3.1.0.jar", "accessPath": "/target/dependency/javax.servlet-api-3.1.0.jar", "annotations": {"evidence": "primary"}}], "licenses": [{"value": "https://glassfish.dev.java.net/nonav/public/CDDL+GPL.html", "spdxExpression": "", "type": "declared", "urls": [], "locations": [{"path": "/target/dependency/javax.servlet-api-3.1.0.jar", "accessPath": "/target/dependency/javax.servlet-api-3.1.0.jar", "annotations": {"evidence": "primary"}}]}], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:glassfish-community:javax.servlet-api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:glassfish-community:javax.servlet_api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:glassfish_community:javax.servlet-api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:glassfish_community:javax.servlet_api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:oracle-corporation:javax.servlet-api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:oracle-corporation:javax.servlet_api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:oracle_corporation:javax.servlet-api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:oracle_corporation:javax.servlet_api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet-api:javax.servlet-api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet-api:javax.servlet_api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet_api:javax.servlet-api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet_api:javax.servlet_api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet:javax.servlet-api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet:javax.servlet_api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:org.glassfish:javax.servlet-api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:org.glassfish:javax.servlet_api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:glassfish:javax.servlet-api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:glassfish:javax.servlet_api:3.1.0:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/javax.servlet/javax.servlet-api@3.1.0", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/javax.servlet-api-3.1.0.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Export-Package", "value": "javax.servlet;uses:=\"javax.servlet.descriptor,javax.servlet.annotation\";version=\"3.1.0\",javax.servlet.descriptor;version=\"3.1.0\",javax.servlet.annotation;uses:=\"javax.servlet\";version=\"3.1.0\",javax.servlet.http;uses:=\"javax.servlet\";version=\"3.1.0\""}, {"key": "Implementation-Version", "value": "3.1.0"}, {"key": "Built-By", "value": "shichan"}, {"key": "Specification-<PERSON><PERSON><PERSON>", "value": "Oracle Corporation"}, {"key": "Tool", "value": "Bnd-0.0.255"}, {"key": "Bundle-Name", "value": "Java Servlet API"}, {"key": "Created-By", "value": "1.7.0_13 (Oracle Corporation)"}, {"key": "Bundle-Vendor", "value": "GlassFish Community"}, {"key": "Implementation-Vendor", "value": "GlassFish Community"}, {"key": "Implementation-Vendor-Id", "value": "org.glassfish"}, {"key": "Bundle-Version", "value": "3.1.0"}, {"key": "Build-Jdk", "value": "1.7.0_13"}, {"key": "Bnd-LastModified", "value": "1366933945581"}, {"key": "Bundle-ManifestVersion", "value": "2"}, {"key": "Bundle-License", "value": "https://glassfish.dev.java.net/nonav/public/CDDL+GPL.html"}, {"key": "Bundle-Description", "value": "Java(TM) Servlet 3.1 API Design Specification"}, {"key": "Specification-Version", "value": "3.1"}, {"key": "Import-Package", "value": "javax.servlet;version=\"3.1.0\",javax.servlet.annotation;version=\"3.1.0\",javax.servlet.descriptor;version=\"3.1.0\",javax.servlet.http;version=\"3.1.0\""}, {"key": "Bundle-SymbolicName", "value": "javax.servlet-api"}, {"key": "Bundle-DocURL", "value": "https://glassfish.dev.java.net"}, {"key": "Extension-Name", "value": "javax.servlet"}, {"key": "Archiver-Version", "value": "Plexus Archiver"}]}, "pomProperties": {"path": "META-INF/maven/javax.servlet/javax.servlet-api/pom.properties", "name": "", "groupId": "javax.servlet", "artifactId": "javax.servlet-api", "version": "3.1.0"}, "digest": [{"algorithm": "sha1", "value": "3cd63d075497751784b2fa84be59432f4905bf7c"}]}}, {"id": "0753aa97cfe8bef4", "name": "javax.servlet.jsp-api", "version": "2.3.3", "type": "java-archive", "foundBy": "java-pom-cataloger", "locations": [{"path": "/pom.xml", "accessPath": "/pom.xml", "annotations": {"evidence": "primary"}}], "licenses": [], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:javax.servlet.jsp-api:javax.servlet.jsp-api:2.3.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet.jsp-api:javax.servlet.jsp_api:2.3.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet.jsp_api:javax.servlet.jsp-api:2.3.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet.jsp_api:javax.servlet.jsp_api:2.3.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet.jsp:javax.servlet.jsp-api:2.3.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet.jsp:javax.servlet.jsp_api:2.3.3:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/javax.servlet.jsp/javax.servlet.jsp-api@2.3.3", "metadataType": "java-archive", "metadata": {"virtualPath": "", "pomProperties": {"path": "", "name": "", "groupId": "javax.servlet.jsp", "artifactId": "javax.servlet.jsp-api", "version": "", "scope": "provided"}}}, {"id": "500585d6df9f3ad7", "name": "javax.servlet.jsp-api", "version": "2.3.3", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/javax.servlet.jsp-api-2.3.3.jar", "accessPath": "/target/dependency/javax.servlet.jsp-api-2.3.3.jar", "annotations": {"evidence": "primary"}}], "licenses": [{"value": "://oss.oracle.com/licenses/CDDL+GPL-1.1", "spdxExpression": "", "type": "declared", "urls": [], "locations": [{"path": "/target/dependency/javax.servlet.jsp-api-2.3.3.jar", "accessPath": "/target/dependency/javax.servlet.jsp-api-2.3.3.jar", "annotations": {"evidence": "primary"}}]}], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:javax.servlet.jsp-api:javax.servlet.jsp-api:2.3.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet.jsp-api:javax.servlet.jsp_api:2.3.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet.jsp_api:javax.servlet.jsp-api:2.3.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet.jsp_api:javax.servlet.jsp_api:2.3.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:oracle-corporation:javax.servlet.jsp-api:2.3.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:oracle-corporation:javax.servlet.jsp_api:2.3.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:oracle_corporation:javax.servlet.jsp-api:2.3.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:oracle_corporation:javax.servlet.jsp_api:2.3.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet.jsp:javax.servlet.jsp-api:2.3.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:javax.servlet.jsp:javax.servlet.jsp_api:2.3.3:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/javax.servlet.jsp/javax.servlet.jsp-api@2.3.3", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/javax.servlet.jsp-api-2.3.3.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Export-Package", "value": "javax.servlet.jsp.el;uses:=\"javax.servlet,javax.el,javax.servlet.jsp,javax.servlet.http\";version=\"2.3.3\",javax.servlet.jsp.tagext;uses:=\"javax.servlet.jsp\";version=\"2.3.3\",javax.servlet.jsp;uses:=\"javax.servlet,javax.el,javax.servlet.jsp.el,javax.servlet.jsp.tagext,javax.servlet.http\";version=\"2.3.3\""}, {"key": "Implementation-Version", "value": "2.3.3"}, {"key": "Specification-<PERSON><PERSON><PERSON>", "value": "Oracle Corporation"}, {"key": "Built-By", "value": "vinay"}, {"key": "Tool", "value": "Bnd-0.0.255"}, {"key": "Bundle-Name", "value": "JavaServer Pages(TM) API"}, {"key": "Created-By", "value": "1.7.0_80 (Oracle Corporation)"}, {"key": "Bundle-Vendor", "value": "Oracle"}, {"key": "Implementation-Vendor", "value": "Oracle Corporation"}, {"key": "Bundle-Version", "value": "2.3.3"}, {"key": "Build-Jdk", "value": "1.7.0_80"}, {"key": "Bnd-LastModified", "value": "1533108124599"}, {"key": "Bundle-ManifestVersion", "value": "2"}, {"key": "Bundle-Description", "value": "Java.net - The Source for Java Technology Collaboration"}, {"key": "Bundle-License", "value": "://oss.oracle.com/licenses/CDDL+GPL-1.1"}, {"key": "Import-Package", "value": "javax.el,javax.servlet,javax.servlet.http,javax.servlet.jsp;version=\"2.3.3\",javax.servlet.jsp.el;version=\"2.3.3\",javax.servlet.jsp.tagext;version=\"2.3.3\""}, {"key": "Bundle-SymbolicName", "value": "javax.servlet.jsp-api"}, {"key": "Bundle-DocURL", "value": "http://www.oracle.com"}, {"key": "Specification-Version", "value": "2.3"}, {"key": "Extension-Name", "value": "javax.servlet.jsp"}]}, "pomProperties": {"path": "META-INF/maven/javax.servlet.jsp/javax.servlet.jsp-api/pom.properties", "name": "", "groupId": "javax.servlet.jsp", "artifactId": "javax.servlet.jsp-api", "version": "2.3.3"}, "digest": [{"algorithm": "sha1", "value": "81191ab80e342912dc9cea735c30ff4eddc64de3"}]}}, {"id": "ef47a662b1676ece", "name": "json-simple", "version": "1.1.1", "type": "java-archive", "foundBy": "java-pom-cataloger", "locations": [{"path": "/pom.xml", "accessPath": "/pom.xml", "annotations": {"evidence": "primary"}}], "licenses": [], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:com.googlecode.json-simple:json-simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.googlecode.json-simple:json_simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:json-simple:json-simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:json-simple:json_simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:json_simple:json-simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:json_simple:json_simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:googlecode:json-simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:googlecode:json_simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:json:json-simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:json:json_simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/com.googlecode.json-simple/json-simple@1.1.1", "metadataType": "java-archive", "metadata": {"virtualPath": "", "pomProperties": {"path": "", "name": "", "groupId": "com.googlecode.json-simple", "artifactId": "json-simple", "version": ""}}}, {"id": "839d1f5093f2747d", "name": "json-simple", "version": "1.1.1", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/json-simple-1.1.1.jar", "accessPath": "/target/dependency/json-simple-1.1.1.jar", "annotations": {"evidence": "primary"}}], "licenses": [{"value": "http://www.apache.org/licenses/LICENSE-2.0.txt", "spdxExpression": "", "type": "declared", "urls": [], "locations": [{"path": "/target/dependency/json-simple-1.1.1.jar", "accessPath": "/target/dependency/json-simple-1.1.1.jar", "annotations": {"evidence": "primary"}}]}], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:com.googlecode.json-simple:json-simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.googlecode.json-simple:json_simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:json-simple:json-simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:json-simple:json_simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:json_simple:json-simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:json_simple:json_simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:googlecode:json-simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:googlecode:json_simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:json:json-simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:json:json_simple:1.1.1:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/com.googlecode.json-simple/json-simple@1.1.1", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/json-simple-1.1.1.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Bnd-LastModified", "value": "1332231819873"}, {"key": "Build-Jdk", "value": "1.6.0_26"}, {"key": "Built-By", "value": "fangyidong"}, {"key": "Bundle-Description", "value": "A simple Java toolkit for JSON"}, {"key": "Bundle-License", "value": "http://www.apache.org/licenses/LICENSE-2.0.txt"}, {"key": "Bundle-ManifestVersion", "value": "2"}, {"key": "Bundle-Name", "value": "JSON.simple"}, {"key": "Bundle-SymbolicName", "value": "com.googlecode.json-simple"}, {"key": "Bundle-Version", "value": "1.1.1"}, {"key": "Created-By", "value": "Apache Maven B<PERSON>le Plugin"}, {"key": "Export-Package", "value": "org.json.simple;uses:=\"org.json.simple.parser\";version=\"1.1.1\",org.json.simple.parser;uses:=\"org.json.simple\";version=\"1.1.1\""}, {"key": "Tool", "value": "Bnd-1.50.0"}]}, "pomProperties": {"path": "META-INF/maven/com.googlecode.json-simple/json-simple/pom.properties", "name": "", "groupId": "com.googlecode.json-simple", "artifactId": "json-simple", "version": "1.1.1"}, "digest": [{"algorithm": "sha1", "value": "c9ad4a0850ab676c5c64461a05ca524cdfff59f1"}]}}, {"id": "eaf71ad5b5315431", "name": "jsp-health-check", "version": "1.0.0", "type": "java-archive", "foundBy": "java-pom-cataloger", "locations": [{"path": "/pom.xml", "accessPath": "/pom.xml", "annotations": {"evidence": "primary"}}], "licenses": [], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:jsp-health-check:jsp-health-check:1.0.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jsp-health-check:jsp_health_check:1.0.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jsp_health_check:jsp-health-check:1.0.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jsp_health_check:jsp_health_check:1.0.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.example:jsp-health-check:1.0.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:com.example:jsp_health_check:1.0.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jsp-health:jsp-health-check:1.0.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jsp-health:jsp_health_check:1.0.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jsp_health:jsp-health-check:1.0.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jsp_health:jsp_health_check:1.0.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:example:jsp-health-check:1.0.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:example:jsp_health_check:1.0.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jsp:jsp-health-check:1.0.0:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:jsp:jsp_health_check:1.0.0:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/com.example/jsp-health-check@1.0.0", "metadataType": "java-archive", "metadata": {"virtualPath": "", "pomProject": {"path": "", "groupId": "com.example", "artifactId": "jsp-health-check", "version": "1.0.0", "name": "JSP Health Check Service", "description": "A simple JSP-based health check service with JSON support"}}}, {"id": "d3877657428d7f33", "name": "junit", "version": "4.12", "type": "java-archive", "foundBy": "java-pom-cataloger", "locations": [{"path": "/pom.xml", "accessPath": "/pom.xml", "annotations": {"evidence": "primary"}}], "licenses": [], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:junit:junit:4.12:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/junit/junit@4.12", "metadataType": "java-archive", "metadata": {"virtualPath": "", "pomProperties": {"path": "", "name": "", "groupId": "junit", "artifactId": "junit", "version": "", "scope": "test"}}}, {"id": "e9d85faf74fc124a", "name": "junit", "version": "4.12", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/junit-4.12.jar", "accessPath": "/target/dependency/junit-4.12.jar", "annotations": {"evidence": "primary"}}], "licenses": [], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:junit:junit:4.12:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/junit/junit@4.12", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/junit-4.12.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Implementation-Vendor", "value": "JUnit"}, {"key": "Implementation-Title", "value": "JUnit"}, {"key": "Implementation-Version", "value": "4.12"}, {"key": "Implementation-Vendor-Id", "value": "junit"}, {"key": "Built-By", "value": "jenkins"}, {"key": "Build-Jdk", "value": "1.6.0_45"}, {"key": "Created-By", "value": "Apache Maven 3.0.4"}, {"key": "Archiver-Version", "value": "Plexus Archiver"}]}, "digest": [{"algorithm": "sha1", "value": "2973d150c0dc1fefe998f834810d68f278ea58ec"}]}}, {"id": "35ee804b8df916dd", "name": "logback-classic", "version": "1.2.3", "type": "java-archive", "foundBy": "java-pom-cataloger", "locations": [{"path": "/pom.xml", "accessPath": "/pom.xml", "annotations": {"evidence": "primary"}}], "licenses": [], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:logback-classic:logback-classic:1.2.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:logback-classic:logback_classic:1.2.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:logback_classic:logback-classic:1.2.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:logback_classic:logback_classic:1.2.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:logback:logback-classic:1.2.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:logback:logback_classic:1.2.3:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/ch.qos.logback/logback-classic@1.2.3", "metadataType": "java-archive", "metadata": {"virtualPath": "", "pomProperties": {"path": "", "name": "", "groupId": "ch.qos.logback", "artifactId": "logback-classic", "version": ""}}}, {"id": "64d975dbc03b4865", "name": "logback-classic", "version": "1.2.3", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/logback-classic-1.2.3.jar", "accessPath": "/target/dependency/logback-classic-1.2.3.jar", "annotations": {"evidence": "primary"}}], "licenses": [{"value": "http://www.eclipse.org/legal/epl-v10.html, http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html", "spdxExpression": "", "type": "declared", "urls": [], "locations": [{"path": "/target/dependency/logback-classic-1.2.3.jar", "accessPath": "/target/dependency/logback-classic-1.2.3.jar", "annotations": {"evidence": "primary"}}]}], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:logback-classic:logback-classic:1.2.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:logback-classic:logback_classic:1.2.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:logback_classic:logback-classic:1.2.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:logback_classic:logback_classic:1.2.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:logback:logback-classic:1.2.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:logback:logback_classic:1.2.3:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/ch.qos.logback/logback-classic@1.2.3", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/logback-classic-1.2.3.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Export-Package", "value": "ch.qos.logback.classic;version=\"1.2.3\";uses:=\"ch.qos.logback.classic.spi,ch.qos.logback.classic.turbo,ch.qos.logback.core,ch.qos.logback.core.pattern,ch.qos.logback.core.spi,ch.qos.logback.core.status,javax.servlet.http,org.slf4j,org.slf4j.event,org.slf4j.spi\",ch.qos.logback.classic.boolex;version=\"1.2.3\";uses:=\"ch.qos.logback.classic.spi,ch.qos.logback.core.boolex,groovy.lang\",ch.qos.logback.classic.db;version=\"1.2.3\";uses:=\"ch.qos.logback.classic.db.names,ch.qos.logback.classic.spi,ch.qos.logback.core.db\",ch.qos.logback.classic.db.names;version=\"1.2.3\",ch.qos.logback.classic.db.script;version=\"1.2.3\",ch.qos.logback.classic.encoder;version=\"1.2.3\";uses:=\"ch.qos.logback.classic.spi,ch.qos.logback.core.pattern\",ch.qos.logback.classic.filter;version=\"1.2.3\";uses:=\"ch.qos.logback.classic,ch.qos.logback.classic.spi,ch.qos.logback.core.filter,ch.qos.logback.core.spi\",ch.qos.logback.classic.gaffer;version=\"1.2.3\";uses:=\"ch.qos.logback.classic,ch.qos.logback.core,ch.qos.logback.core.spi,groovy.lang,org.codehaus.groovy.control.customizers\",ch.qos.logback.classic.helpers;version=\"1.2.3\";uses:=\"javax.servlet\",ch.qos.logback.classic.html;version=\"1.2.3\";uses:=\"ch.qos.logback.classic.spi,ch.qos.logback.core.html,ch.qos.logback.core.pattern\",ch.qos.logback.classic.jmx;version=\"1.2.3\";uses:=\"ch.qos.logback.classic,ch.qos.logback.classic.spi,ch.qos.logback.core,ch.qos.logback.core.joran.spi,ch.qos.logback.core.spi,javax.management\",ch.qos.logback.classic.joran;version=\"1.2.3\";uses:=\"ch.qos.logback.classic.spi,ch.qos.logback.core.joran,ch.qos.logback.core.joran.spi,ch.qos.logback.core.spi\",ch.qos.logback.classic.joran.action;version=\"1.2.3\";uses:=\"ch.qos.logback.core.joran.action,ch.qos.logback.core.joran.spi,org.xml.sax\",ch.qos.logback.classic.jul;version=\"1.2.3\";uses:=\"ch.qos.logback.classic,ch.qos.logback.classic.spi,ch.qos.logback.core.spi\",ch.qos.logback.classic.layout;version=\"1.2.3\";uses:=\"ch.qos.logback.classic.spi,ch.qos.logback.core\",ch.qos.logback.classic.log4j;version=\"1.2.3\";uses:=\"ch.qos.logback.classic.spi,ch.qos.logback.core\",ch.qos.logback.classic.net;version=\"1.2.3\";uses:=\"ch.qos.logback.classic,ch.qos.logback.classic.spi,ch.qos.logback.core,ch.qos.logback.core.boolex,ch.qos.logback.core.helpers,ch.qos.logback.core.joran.spi,ch.qos.logback.core.net,ch.qos.logback.core.net.ssl,ch.qos.logback.core.pattern,ch.qos.logback.core.spi,javax.net,javax.net.ssl\",ch.qos.logback.classic.net.server;version=\"1.2.3\";uses:=\"ch.qos.logback.classic.net,ch.qos.logback.classic.spi,ch.qos.logback.core.net,ch.qos.logback.core.net.server,ch.qos.logback.core.net.ssl,ch.qos.logback.core.spi,javax.net\",ch.qos.logback.classic.pattern;version=\"1.2.3\";uses:=\"ch.qos.logback.classic.spi,ch.qos.logback.core,ch.qos.logback.core.pattern,org.slf4j\",ch.qos.logback.classic.pattern.color;version=\"1.2.3\";uses:=\"ch.qos.logback.classic.spi,ch.qos.logback.core.pattern.color\",ch.qos.logback.classic.selector;version=\"1.2.3\";uses:=\"ch.qos.logback.classic\",ch.qos.logback.classic.selector.servlet;version=\"1.2.3\";uses:=\"javax.servlet\",ch.qos.logback.classic.servlet;version=\"1.2.3\";uses:=\"javax.servlet\",ch.qos.logback.classic.sift;version=\"1.2.3\";uses:=\"ch.qos.logback.classic.spi,ch.qos.logback.core,ch.qos.logback.core.joran.action,ch.qos.logback.core.joran.event,ch.qos.logback.core.joran.spi,ch.qos.logback.core.sift,org.xml.sax\",ch.qos.logback.classic.spi;version=\"1.2.3\";uses:=\"ch.qos.logback.classic,ch.qos.logback.classic.turbo,ch.qos.logback.core,ch.qos.logback.core.spi,org.slf4j\",ch.qos.logback.classic.turbo;version=\"1.2.3\";uses:=\"ch.qos.logback.classic,ch.qos.logback.core.spi,org.slf4j\",ch.qos.logback.classic.util;version=\"1.2.3\";uses:=\"ch.qos.logback.classic,ch.qos.logback.classic.selector,ch.qos.logback.classic.spi,ch.qos.logback.core.joran.spi,ch.qos.logback.core.status,javax.naming,org.slf4j.spi\",org.slf4j.impl;version=\"1.7.25\";uses:=\"org.slf4j,org.slf4j.spi\""}, {"key": "Built-By", "value": "ceki"}, {"key": "Tool", "value": "Bnd-2.4.1.201501161923"}, {"key": "Bundle-Name", "value": "Logback Classic Module"}, {"key": "Created-By", "value": "Apache Maven B<PERSON>le Plugin"}, {"key": "Bundle-RequiredExecutionEnvironment", "value": "JavaSE-1.6"}, {"key": "Require-Capability", "value": "osgi.ee;filter:=\"(&(osgi.ee=JavaSE)(version=1.6))\""}, {"key": "Bundle-Vendor", "value": "QOS.ch"}, {"key": "Build-Jdk", "value": "1.7.0_80"}, {"key": "Bundle-Version", "value": "1.2.3"}, {"key": "Bnd-LastModified", "value": "1491020859060"}, {"key": "Bundle-ManifestVersion", "value": "2"}, {"key": "Bundle-License", "value": "http://www.eclipse.org/legal/epl-v10.html, http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html"}, {"key": "Bundle-Description", "value": "logback-classic module"}, {"key": "Bundle-SymbolicName", "value": "ch.qos.logback.classic"}, {"key": "Bundle-DocURL", "value": "http://www.qos.ch"}, {"key": "Import-Package", "value": "sun.reflect;resolution:=optional,javax.management;resolution:=optional,javax.naming;resolution:=optional,javax.net;resolution:=optional,javax.net.ssl;resolution:=optional,javax.servlet;resolution:=optional;version=\"[3.1,4)\",javax.servlet.http;resolution:=optional;version=\"[3.1,4)\",org.xml.sax;resolution:=optional,org.slf4j,org.slf4j.event;version=\"[1.7,2)\",ch.qos.logback.core.util;version=\"[1.2,2)\",org.codehaus.groovy.control;resolution:=optional;version=\"[2.4,3)\",org.codehaus.groovy.control.customizers;resolution:=optional;version=\"[2.4,3)\",org.codehaus.groovy.reflection;resolution:=optional;version=\"[2.4,3)\",org.codehaus.groovy.runtime;resolution:=optional;version=\"[2.4,3)\",org.codehaus.groovy.runtime.callsite;resolution:=optional;version=\"[2.4,3)\",org.codehaus.groovy.runtime.typehandling;resolution:=optional;version=\"[2.4,3)\",org.codehaus.groovy.runtime.wrappers;resolution:=optional;version=\"[2.4,3)\",org.codehaus.groovy.transform;resolution:=optional;version=\"[2.4,3)\",groovy.lang;resolution:=optional;version=\"[2.4,3)\",ch.qos.logback.core;version=\"[1.2,2)\",ch.qos.logback.core.boolex;version=\"[1.2,2)\",ch.qos.logback.core.db;version=\"[1.2,2)\",ch.qos.logback.core.encoder;version=\"[1.2,2)\",ch.qos.logback.core.filter;version=\"[1.2,2)\",ch.qos.logback.core.helpers;version=\"[1.2,2)\",ch.qos.logback.core.html;version=\"[1.2,2)\",ch.qos.logback.core.joran;version=\"[1.2,2)\",ch.qos.logback.core.joran.action;version=\"[1.2,2)\",ch.qos.logback.core.joran.conditional;version=\"[1.2,2)\",ch.qos.logback.core.joran.event;version=\"[1.2,2)\",ch.qos.logback.core.joran.spi;version=\"[1.2,2)\",ch.qos.logback.core.joran.util;version=\"[1.2,2)\",ch.qos.logback.core.joran.util.beans;version=\"[1.2,2)\",ch.qos.logback.core.net;version=\"[1.2,2)\",ch.qos.logback.core.net.server;version=\"[1.2,2)\",ch.qos.logback.core.net.ssl;version=\"[1.2,2)\",ch.qos.logback.core.pattern;version=\"[1.2,2)\",ch.qos.logback.core.pattern.color;version=\"[1.2,2)\",ch.qos.logback.core.pattern.parser;version=\"[1.2,2)\",ch.qos.logback.core.sift;version=\"[1.2,2)\",ch.qos.logback.core.spi;version=\"[1.2,2)\",ch.qos.logback.core.status;version=\"[1.2,2)\",org.slf4j.helpers;version=\"[1.7,2)\",org.slf4j.spi;version=\"[1.7,2)\",ch.qos.logback.core.rolling;version=\"[1.2,2)\",ch.qos.logback.core.rolling.helper;version=\"[1.2,2)\",ch.qos.logback.core.read;version=\"[1.2,2)\""}, {"key": "Originally-Created-By", "value": "Apache Maven B<PERSON>le Plugin"}, {"key": "Archiver-Version", "value": "Plexus Archiver"}]}, "pomProperties": {"path": "META-INF/maven/ch.qos.logback/logback-classic/pom.properties", "name": "", "groupId": "ch.qos.logback", "artifactId": "logback-classic", "version": "1.2.3"}, "digest": [{"algorithm": "sha1", "value": "7c4f3c474fb2c041d8028740440937705ebb473a"}]}}, {"id": "2bfc9bfc01d42dd5", "name": "logback-core", "version": "1.2.3", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/logback-core-1.2.3.jar", "accessPath": "/target/dependency/logback-core-1.2.3.jar", "annotations": {"evidence": "primary"}}], "licenses": [{"value": "http://www.eclipse.org/legal/epl-v10.html, http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html", "spdxExpression": "", "type": "declared", "urls": [], "locations": [{"path": "/target/dependency/logback-core-1.2.3.jar", "accessPath": "/target/dependency/logback-core-1.2.3.jar", "annotations": {"evidence": "primary"}}]}], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:logback-core:logback-core:1.2.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:logback-core:logback_core:1.2.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:logback_core:logback-core:1.2.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:logback_core:logback_core:1.2.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:logback:logback-core:1.2.3:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:logback:logback_core:1.2.3:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/ch.qos.logback/logback-core@1.2.3", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/logback-core-1.2.3.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Export-Package", "value": "ch.qos.logback.core;version=\"1.2.3\";uses:=\"ch.qos.logback.core.encoder,ch.qos.logback.core.filter,ch.qos.logback.core.helpers,ch.qos.logback.core.joran.spi,ch.qos.logback.core.spi,ch.qos.logback.core.status,ch.qos.logback.core.util\",ch.qos.logback.core.boolex;version=\"1.2.3\";uses:=\"ch.qos.logback.core.spi\",ch.qos.logback.core.db;version=\"1.2.3\";uses:=\"ch.qos.logback.core,ch.qos.logback.core.db.dialect,ch.qos.logback.core.joran.action,ch.qos.logback.core.joran.spi,ch.qos.logback.core.joran.util.beans,ch.qos.logback.core.spi,javax.sql,org.xml.sax\",ch.qos.logback.core.db.dialect;version=\"1.2.3\";uses:=\"ch.qos.logback.core.spi\",ch.qos.logback.core.encoder;version=\"1.2.3\";uses:=\"ch.qos.logback.core,ch.qos.logback.core.spi\",ch.qos.logback.core.filter;version=\"1.2.3\";uses:=\"ch.qos.logback.core.boolex,ch.qos.logback.core.spi\",ch.qos.logback.core.helpers;version=\"1.2.3\";uses:=\"ch.qos.logback.core\",ch.qos.logback.core.hook;version=\"1.2.3\";uses:=\"ch.qos.logback.core.spi,ch.qos.logback.core.util\",ch.qos.logback.core.html;version=\"1.2.3\";uses:=\"ch.qos.logback.core,ch.qos.logback.core.pattern\",ch.qos.logback.core.joran;version=\"1.2.3\";uses:=\"ch.qos.logback.core,ch.qos.logback.core.joran.event,ch.qos.logback.core.joran.spi,ch.qos.logback.core.joran.util.beans,ch.qos.logback.core.spi,org.xml.sax\",ch.qos.logback.core.joran.action;version=\"1.2.3\";uses:=\"ch.qos.logback.core.joran.spi,ch.qos.logback.core.joran.util,ch.qos.logback.core.joran.util.beans,ch.qos.logback.core.spi,ch.qos.logback.core.util,org.xml.sax\",ch.qos.logback.core.joran.conditional;version=\"1.2.3\";uses:=\"ch.qos.logback.core.joran.action,ch.qos.logback.core.joran.event,ch.qos.logback.core.joran.spi,ch.qos.logback.core.spi,org.codehaus.commons.compiler,org.xml.sax\",ch.qos.logback.core.joran.event;version=\"1.2.3\";uses:=\"ch.qos.logback.core,ch.qos.logback.core.joran.spi,ch.qos.logback.core.spi,ch.qos.logback.core.status,org.xml.sax,org.xml.sax.helpers\",ch.qos.logback.core.joran.event.stax;version=\"1.2.3\";uses:=\"ch.qos.logback.core,ch.qos.logback.core.joran.spi,ch.qos.logback.core.spi,javax.xml.stream,javax.xml.stream.events\",ch.qos.logback.core.joran.node;version=\"1.2.3\",ch.qos.logback.core.joran.spi;version=\"1.2.3\";uses:=\"ch.qos.logback.core,ch.qos.logback.core.joran.action,ch.qos.logback.core.joran.event,ch.qos.logback.core.spi,ch.qos.logback.core.status,org.xml.sax\",ch.qos.logback.core.joran.util;version=\"1.2.3\";uses:=\"ch.qos.logback.core,ch.qos.logback.core.joran.spi,ch.qos.logback.core.joran.util.beans,ch.qos.logback.core.spi,ch.qos.logback.core.util\",ch.qos.logback.core.joran.util.beans;version=\"1.2.3\";uses:=\"ch.qos.logback.core,ch.qos.logback.core.spi\",ch.qos.logback.core.layout;version=\"1.2.3\";uses:=\"ch.qos.logback.core\",ch.qos.logback.core.net;version=\"1.2.3\";uses:=\"ch.qos.logback.core,ch.qos.logback.core.boolex,ch.qos.logback.core.helpers,ch.qos.logback.core.net.ssl,ch.qos.logback.core.pattern,ch.qos.logback.core.sift,ch.qos.logback.core.spi,ch.qos.logback.core.util,javax.mail,javax.net\",ch.qos.logback.core.net.server;version=\"1.2.3\";uses:=\"ch.qos.logback.core,ch.qos.logback.core.net.ssl,ch.qos.logback.core.spi,javax.net\",ch.qos.logback.core.net.ssl;version=\"1.2.3\";uses:=\"ch.qos.logback.core.joran.spi,ch.qos.logback.core.spi,javax.net,javax.net.ssl\",ch.qos.logback.core.pattern;version=\"1.2.3\";uses:=\"ch.qos.logback.core,ch.qos.logback.core.encoder,ch.qos.logback.core.spi,ch.qos.logback.core.status\",ch.qos.logback.core.pattern.color;version=\"1.2.3\";uses:=\"ch.qos.logback.core.pattern\",ch.qos.logback.core.pattern.parser;version=\"1.2.3\";uses:=\"ch.qos.logback.core.pattern,ch.qos.logback.core.pattern.util,ch.qos.logback.core.spi\",ch.qos.logback.core.pattern.util;version=\"1.2.3\",ch.qos.logback.core.property;version=\"1.2.3\";uses:=\"ch.qos.logback.core\",ch.qos.logback.core.read;version=\"1.2.3\";uses:=\"ch.qos.logback.core\",ch.qos.logback.core.recovery;version=\"1.2.3\";uses:=\"ch.qos.logback.core,ch.qos.logback.core.status\",ch.qos.logback.core.rolling;version=\"1.2.3\";uses:=\"ch.qos.logback.core,ch.qos.logback.core.joran.spi,ch.qos.logback.core.rolling.helper,ch.qos.logback.core.spi,ch.qos.logback.core.util\",ch.qos.logback.core.rolling.helper;version=\"1.2.3\";uses:=\"ch.qos.logback.core,ch.qos.logback.core.pattern,ch.qos.logback.core.rolling,ch.qos.logback.core.spi\",ch.qos.logback.core.sift;version=\"1.2.3\";uses:=\"ch.qos.logback.core,ch.qos.logback.core.joran,ch.qos.logback.core.joran.event,ch.qos.logback.core.joran.spi,ch.qos.logback.core.spi,ch.qos.logback.core.util\",ch.qos.logback.core.spi;version=\"1.2.3\";uses:=\"ch.qos.logback.core,ch.qos.logback.core.filter,ch.qos.logback.core.helpers,ch.qos.logback.core.status\",ch.qos.logback.core.status;version=\"1.2.3\";uses:=\"ch.qos.logback.core,ch.qos.logback.core.spi,javax.servlet,javax.servlet.http\",ch.qos.logback.core.subst;version=\"1.2.3\";uses:=\"ch.qos.logback.core.spi\",ch.qos.logback.core.util;version=\"1.2.3\";uses:=\"ch.qos.logback.core,ch.qos.logback.core.rolling,ch.qos.logback.core.rolling.helper,ch.qos.logback.core.spi,ch.qos.logback.core.status\""}, {"key": "Built-By", "value": "ceki"}, {"key": "Tool", "value": "Bnd-2.4.1.201501161923"}, {"key": "Bundle-Name", "value": "Logback Core Module"}, {"key": "Created-By", "value": "Apache Maven B<PERSON>le Plugin"}, {"key": "Bundle-RequiredExecutionEnvironment", "value": "JavaSE-1.6"}, {"key": "Require-Capability", "value": "osgi.ee;filter:=\"(&(osgi.ee=JavaSE)(version=1.6))\""}, {"key": "Bundle-Vendor", "value": "QOS.ch"}, {"key": "Build-Jdk", "value": "1.7.0_80"}, {"key": "Bundle-Version", "value": "1.2.3"}, {"key": "Bnd-LastModified", "value": "1491020827186"}, {"key": "Bundle-ManifestVersion", "value": "2"}, {"key": "Bundle-License", "value": "http://www.eclipse.org/legal/epl-v10.html, http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html"}, {"key": "Bundle-Description", "value": "logback-core module"}, {"key": "Bundle-SymbolicName", "value": "ch.qos.logback.core"}, {"key": "Bundle-DocURL", "value": "http://www.qos.ch"}, {"key": "Import-Package", "value": "javax.mail;resolution:=optional,javax.mail.internet;resolution:=optional,javax.naming;resolution:=optional,javax.net;resolution:=optional,javax.net.ssl;resolution:=optional,javax.servlet;resolution:=optional;version=\"[3.1,4)\",javax.servlet.http;resolution:=optional;version=\"[3.1,4)\",javax.sql;resolution:=optional,javax.xml.namespace;resolution:=optional,javax.xml.parsers;resolution:=optional,javax.xml.stream;resolution:=optional,javax.xml.stream.events;resolution:=optional,org.xml.sax;resolution:=optional,org.xml.sax.helpers;resolution:=optional,org.codehaus.janino;resolution:=optional,org.codehaus.commons.compiler;resolution:=optional,org.fusesource.jansi;resolution:=optional;version=\"[1.9,2)\""}, {"key": "Originally-Created-By", "value": "Apache Maven B<PERSON>le Plugin"}, {"key": "Archiver-Version", "value": "Plexus Archiver"}]}, "pomProperties": {"path": "META-INF/maven/ch.qos.logback/logback-core/pom.properties", "name": "", "groupId": "ch.qos.logback", "artifactId": "logback-core", "version": "1.2.3"}, "digest": [{"algorithm": "sha1", "value": "864344400c3d4d92dfeb0a305dc87d953677c03c"}]}}, {"id": "ab66f9a093724bd0", "name": "ognl", "version": "3.1.26", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/ognl-3.1.26.jar", "accessPath": "/target/dependency/ognl-3.1.26.jar", "annotations": {"evidence": "primary"}}], "licenses": [{"value": "The Apache Software License, Version 2.0", "spdxExpression": "", "type": "declared", "urls": ["http://www.apache.org/licenses/LICENSE-2.0.txt"], "locations": [{"path": "/target/dependency/ognl-3.1.26.jar", "accessPath": "/target/dependency/ognl-3.1.26.jar", "annotations": {"evidence": "primary"}}]}], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:ognl:ognl:3.1.26:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/ognl/ognl@3.1.26", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/ognl-3.1.26.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Automatic-Module-Name", "value": "ognl"}, {"key": "Built-By", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"key": "Build-Jdk", "value": "1.7.0_80"}, {"key": "Created-By", "value": "Apache Maven 3.5.2"}, {"key": "Archiver-Version", "value": "Plexus Archiver"}]}, "pomProperties": {"path": "META-INF/maven/ognl/ognl/pom.properties", "name": "", "groupId": "ognl", "artifactId": "ognl", "version": "3.1.26"}, "digest": [{"algorithm": "sha1", "value": "922d3d922b8aa40146d842114c184c8b403d2f4f"}]}}, {"id": "1eb294e895425f78", "name": "slf4j-api", "version": "1.7.30", "type": "java-archive", "foundBy": "java-pom-cataloger", "locations": [{"path": "/pom.xml", "accessPath": "/pom.xml", "annotations": {"evidence": "primary"}}], "licenses": [], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:org.slf4j:slf4j-api:1.7.30:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:org.slf4j:slf4j_api:1.7.30:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:slf4j-api:slf4j-api:1.7.30:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:slf4j-api:slf4j_api:1.7.30:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:slf4j_api:slf4j-api:1.7.30:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:slf4j_api:slf4j_api:1.7.30:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:slf4j:slf4j-api:1.7.30:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:slf4j:slf4j_api:1.7.30:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/org.slf4j/slf4j-api@1.7.30", "metadataType": "java-archive", "metadata": {"virtualPath": "", "pomProperties": {"path": "", "name": "", "groupId": "org.slf4j", "artifactId": "slf4j-api", "version": ""}}}, {"id": "34070c4539bb7594", "name": "slf4j-api", "version": "1.7.30", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/slf4j-api-1.7.30.jar", "accessPath": "/target/dependency/slf4j-api-1.7.30.jar", "annotations": {"evidence": "primary"}}], "licenses": [], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:org.slf4j:slf4j-api:1.7.30:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:org.slf4j:slf4j_api:1.7.30:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:slf4j-api:slf4j-api:1.7.30:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:slf4j-api:slf4j_api:1.7.30:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:slf4j_api:slf4j-api:1.7.30:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:slf4j_api:slf4j_api:1.7.30:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:slf4j:slf4j-api:1.7.30:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:slf4j:slf4j_api:1.7.30:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/org.slf4j/slf4j-api@1.7.30", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/slf4j-api-1.7.30.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Archiver-Version", "value": "Plexus Archiver"}, {"key": "Created-By", "value": "Apache Maven"}, {"key": "Built-By", "value": "ceki"}, {"key": "Build-Jdk", "value": "1.8.0_121"}, {"key": "Bundle-Description", "value": "The slf4j API"}, {"key": "Bundle-Version", "value": "1.7.30"}, {"key": "Implementation-Version", "value": "1.7.30"}, {"key": "X-Compile-Source-JDK", "value": "1.5"}, {"key": "X-Compile-Target-JDK", "value": "1.5"}, {"key": "Implementation-Title", "value": "slf4j-api"}, {"key": "Bundle-ManifestVersion", "value": "2"}, {"key": "Bundle-SymbolicName", "value": "slf4j.api"}, {"key": "Bundle-Name", "value": "slf4j-api"}, {"key": "Bundle-Vendor", "value": "SLF4J.ORG"}, {"key": "Bundle-RequiredExecutionEnvironment", "value": "J2SE-1.5"}, {"key": "Automatic-Module-Name", "value": "org.slf4j"}, {"key": "Export-Package", "value": "org.slf4j;version=1.7.30, org.slf4j.spi;version=1.7.30, org.slf4j.helpers;version=1.7.30, org.slf4j.event;version=1.7.30"}, {"key": "Import-Package", "value": "org.slf4j.impl;version=1.6.0"}]}, "pomProperties": {"path": "META-INF/maven/org.slf4j/slf4j-api/pom.properties", "name": "", "groupId": "org.slf4j", "artifactId": "slf4j-api", "version": "1.7.30"}, "digest": [{"algorithm": "sha1", "value": "b5a4b6d16ab13e34a88fae84c35cd5d68cac922c"}]}}, {"id": "fa3a958a5056b332", "name": "thymel<PERSON><PERSON>", "version": "3.0.15.RELEASE", "type": "java-archive", "foundBy": "java-pom-cataloger", "locations": [{"path": "/pom.xml", "accessPath": "/pom.xml", "annotations": {"evidence": "primary"}}], "licenses": [], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:org.thymeleaf:thymeleaf:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf:thymeleaf:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/org.thymeleaf/<EMAIL>", "metadataType": "java-archive", "metadata": {"virtualPath": "", "pomProperties": {"path": "", "name": "", "groupId": "org.thymeleaf", "artifactId": "thymel<PERSON><PERSON>", "version": ""}}}, {"id": "90f49a2feacd69a0", "name": "thymel<PERSON><PERSON>", "version": "3.0.15.RELEASE", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/thymeleaf-3.0.15.RELEASE.jar", "accessPath": "/target/dependency/thymeleaf-3.0.15.RELEASE.jar", "annotations": {"evidence": "primary"}}], "licenses": [{"value": "Apache-2.0", "spdxExpression": "Apache-2.0", "type": "concluded", "urls": [], "locations": [{"path": "/target/dependency/thymeleaf-3.0.15.RELEASE.jar", "accessPath": "/target/dependency/thymeleaf-3.0.15.RELEASE.jar", "annotations": {"evidence": "primary"}}]}], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:thymeleaf-team:thymeleaf:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_team:thymeleaf:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf:thymeleaf:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/thymeleaf/<EMAIL>", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/thymeleaf-3.0.15.RELEASE.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Created-By", "value": "<PERSON><PERSON> 3.2.0"}, {"key": "Build-Jdk-Spec", "value": "11"}, {"key": "Specification-Title", "value": "thymel<PERSON><PERSON>"}, {"key": "Specification-Version", "value": "3.0"}, {"key": "Specification-<PERSON><PERSON><PERSON>", "value": "The THYMELEAF team"}, {"key": "Implementation-Title", "value": "thymel<PERSON><PERSON>"}, {"key": "Implementation-Version", "value": "3.0.15.RELEASE"}, {"key": "Implementation-Vendor", "value": "The THYMELEAF team"}, {"key": "Automatic-Module-Name", "value": "thymel<PERSON><PERSON>"}, {"key": "Built-By", "value": "thymel<PERSON><PERSON>"}, {"key": "X-Compile-Source-JDK", "value": "6"}, {"key": "X-Compile-Target-JDK", "value": "6"}]}, "digest": [{"algorithm": "sha1", "value": "13e3296a03d8a597b734d832ed8656139bf9cdd8"}]}}, {"id": "c4d4517469d619be", "name": "thymeleaf-extras-java8time", "version": "3.0.4.RELEASE", "type": "java-archive", "foundBy": "java-pom-cataloger", "locations": [{"path": "/pom.xml", "accessPath": "/pom.xml", "annotations": {"evidence": "primary"}}], "licenses": [], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:thymeleaf-extras-java8time:thymeleaf-extras-java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf-extras-java8time:thymeleaf_extras_java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_extras_java8time:thymeleaf-extras-java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_extras_java8time:thymeleaf_extras_java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:org.thymeleaf.extras:thymeleaf-extras-java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:org.thymeleaf.extras:thymeleaf_extras_java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf-extras:thymeleaf-extras-java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf-extras:thymeleaf_extras_java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_extras:thymeleaf-extras-java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_extras:thymeleaf_extras_java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf:thymeleaf-extras-java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf:thymeleaf_extras_java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:extras:thymeleaf-extras-java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:extras:thymeleaf_extras_java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/org.thymeleaf.extras/<EMAIL>", "metadataType": "java-archive", "metadata": {"virtualPath": "", "pomProperties": {"path": "", "name": "", "groupId": "org.thymeleaf.extras", "artifactId": "thymeleaf-extras-java8time", "version": ""}}}, {"id": "822e5bf35009365f", "name": "thymeleaf-extras-java8time", "version": "3.0.4.RELEASE", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/thymeleaf-extras-java8time-3.0.4.RELEASE.jar", "accessPath": "/target/dependency/thymeleaf-extras-java8time-3.0.4.RELEASE.jar", "annotations": {"evidence": "primary"}}], "licenses": [{"value": "Apache-2.0", "spdxExpression": "Apache-2.0", "type": "concluded", "urls": [], "locations": [{"path": "/target/dependency/thymeleaf-extras-java8time-3.0.4.RELEASE.jar", "accessPath": "/target/dependency/thymeleaf-extras-java8time-3.0.4.RELEASE.jar", "annotations": {"evidence": "primary"}}]}], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:thymeleaf-extras-java8time:thymeleaf-extras-java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf-extras-java8time:thymeleaf_extras_java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_extras_java8time:thymeleaf-extras-java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_extras_java8time:thymeleaf_extras_java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:org.thymeleaf.extras:thymeleaf-extras-java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:org.thymeleaf.extras:thymeleaf_extras_java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf-extras:thymeleaf-extras-java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf-extras:thymeleaf_extras_java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_extras:thymeleaf-extras-java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_extras:thymeleaf_extras_java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf-team:thymeleaf-extras-java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf-team:thymeleaf_extras_java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_team:thymeleaf-extras-java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_team:thymeleaf_extras_java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf:thymeleaf-extras-java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf:thymeleaf_extras_java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:extras:thymeleaf-extras-java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:extras:thymeleaf_extras_java8time:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf-extras-java8time:extras:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_extras_java8time:extras:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:org.thymeleaf.extras:extras:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf-extras:extras:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_extras:extras:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf-team:extras:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_team:extras:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf:extras:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:extras:extras:3.0.4.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/org.thymeleaf.extras/<EMAIL>", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/thymeleaf-extras-java8time-3.0.4.RELEASE.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Created-By", "value": "Apache Maven 3.6.0"}, {"key": "Build-Jdk", "value": "11.0.2"}, {"key": "Specification-Title", "value": "thymeleaf-extras-java8time"}, {"key": "Specification-Version", "value": "3.0"}, {"key": "Specification-<PERSON><PERSON><PERSON>", "value": "The THYMELEAF team"}, {"key": "Implementation-Title", "value": "thymeleaf-extras-java8time"}, {"key": "Implementation-Version", "value": "3.0.4.RELEASE"}, {"key": "Implementation-Vendor-Id", "value": "org.thymeleaf.extras"}, {"key": "Implementation-Vendor", "value": "The THYMELEAF team"}, {"key": "Implementation-URL", "value": "http://www.thymeleaf.org"}, {"key": "Automatic-Module-Name", "value": "thymeleaf.extras.java8time"}, {"key": "Built-By", "value": "thymel<PERSON><PERSON>"}, {"key": "X-Compile-Source-JDK", "value": "1.8"}, {"key": "X-Compile-Target-JDK", "value": "1.8"}]}, "digest": [{"algorithm": "sha1", "value": "36e7175ddce36c486fff4578b5af7bb32f54f5df"}]}}, {"id": "2d9a8e13f9915139", "name": "thymeleaf-spring5", "version": "3.0.15.RELEASE", "type": "java-archive", "foundBy": "java-pom-cataloger", "locations": [{"path": "/pom.xml", "accessPath": "/pom.xml", "annotations": {"evidence": "primary"}}], "licenses": [], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:thymeleaf-spring5:thymeleaf-spring5:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf-spring5:thymeleaf_spring5:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_spring5:thymeleaf-spring5:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_spring5:thymeleaf_spring5:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:org.thymeleaf:thymeleaf-spring5:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:org.thymeleaf:thymeleaf_spring5:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf:thymeleaf-spring5:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf:thymeleaf_spring5:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/org.thymeleaf/<EMAIL>", "metadataType": "java-archive", "metadata": {"virtualPath": "", "pomProperties": {"path": "", "name": "", "groupId": "org.thymeleaf", "artifactId": "thymeleaf-spring5", "version": ""}}}, {"id": "064b8f8c2b61b25a", "name": "thymeleaf-spring5", "version": "3.0.15.RELEASE", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/thymeleaf-spring5-3.0.15.RELEASE.jar", "accessPath": "/target/dependency/thymeleaf-spring5-3.0.15.RELEASE.jar", "annotations": {"evidence": "primary"}}], "licenses": [{"value": "Apache-2.0", "spdxExpression": "Apache-2.0", "type": "concluded", "urls": [], "locations": [{"path": "/target/dependency/thymeleaf-spring5-3.0.15.RELEASE.jar", "accessPath": "/target/dependency/thymeleaf-spring5-3.0.15.RELEASE.jar", "annotations": {"evidence": "primary"}}]}], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:thymeleaf-spring5:thymeleaf-spring5:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf-spring5:thymeleaf_spring5:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_spring5:thymeleaf-spring5:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_spring5:thymeleaf_spring5:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf-team:thymeleaf-spring5:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf-team:thymeleaf_spring5:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_team:thymeleaf-spring5:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf_team:thymeleaf_spring5:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf:thymeleaf-spring5:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:thymeleaf:thymeleaf_spring5:3.0.15.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/thymeleaf-spring5/<EMAIL>", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/thymeleaf-spring5-3.0.15.RELEASE.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Created-By", "value": "<PERSON><PERSON> 3.2.0"}, {"key": "Build-Jdk-Spec", "value": "11"}, {"key": "Specification-Title", "value": "thymeleaf-spring5"}, {"key": "Specification-Version", "value": "3.0"}, {"key": "Specification-<PERSON><PERSON><PERSON>", "value": "The THYMELEAF team"}, {"key": "Implementation-Title", "value": "thymeleaf-spring5"}, {"key": "Implementation-Version", "value": "3.0.15.RELEASE"}, {"key": "Implementation-Vendor", "value": "The THYMELEAF team"}, {"key": "Automatic-Module-Name", "value": "thymeleaf.spring5"}, {"key": "Built-By", "value": "thymel<PERSON><PERSON>"}, {"key": "X-Compile-Source-JDK", "value": "8"}, {"key": "X-Compile-Target-JDK", "value": "8"}]}, "digest": [{"algorithm": "sha1", "value": "7170e1bcd1588d38c139f7048ebcc262676441c3"}]}}, {"id": "0344c250a9ed24c4", "name": "unbescape", "version": "1.1.6.RELEASE", "type": "java-archive", "foundBy": "java-archive-cataloger", "locations": [{"path": "/target/dependency/unbescape-1.1.6.RELEASE.jar", "accessPath": "/target/dependency/unbescape-1.1.6.RELEASE.jar", "annotations": {"evidence": "primary"}}], "licenses": [{"value": "http://www.apache.org/licenses/LICENSE-2.0.txt", "spdxExpression": "", "type": "declared", "urls": [], "locations": [{"path": "/target/dependency/unbescape-1.1.6.RELEASE.jar", "accessPath": "/target/dependency/unbescape-1.1.6.RELEASE.jar", "annotations": {"evidence": "primary"}}]}], "language": "java", "cpes": [{"cpe": "cpe:2.3:a:unbescape-team:unbescape:1.1.6.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:unbescape_team:unbescape:1.1.6.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:org.unbescape:unbescape:1.1.6.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}, {"cpe": "cpe:2.3:a:unbescape:unbescape:1.1.6.RELEASE:*:*:*:*:*:*:*", "source": "syft-generated"}], "purl": "pkg:maven/org.unbescape/<EMAIL>", "metadataType": "java-archive", "metadata": {"virtualPath": "/target/dependency/unbescape-1.1.6.RELEASE.jar", "manifest": {"main": [{"key": "Manifest-Version", "value": "1.0"}, {"key": "Created-By", "value": "Apache Maven B<PERSON>le Plugin"}, {"key": "Built-By", "value": "releases"}, {"key": "Build-Jdk", "value": "10"}, {"key": "Specification-Title", "value": "unbescape"}, {"key": "Specification-Version", "value": "1.1.6.RELEASE"}, {"key": "Specification-<PERSON><PERSON><PERSON>", "value": "The UNBESCAPE team"}, {"key": "Implementation-Title", "value": "unbescape"}, {"key": "Implementation-Version", "value": "1.1.6.RELEASE"}, {"key": "Implementation-Vendor-Id", "value": "org.unbescape"}, {"key": "Implementation-Vendor", "value": "The UNBESCAPE team"}, {"key": "Implementation-URL", "value": "http://www.unbescape.org"}, {"key": "Automatic-Module-Name", "value": "unbescape"}, {"key": "X-Compile-Source-JDK", "value": "6"}, {"key": "X-Compile-Target-JDK", "value": "6"}, {"key": "Bnd-LastModified", "value": "1522429466076"}, {"key": "Bundle-Description", "value": "Advanced yet easy-to-use escape/unescape library for Java"}, {"key": "Bundle-DocURL", "value": "http://www.unbescape.org"}, {"key": "Bundle-License", "value": "http://www.apache.org/licenses/LICENSE-2.0.txt"}, {"key": "Bundle-ManifestVersion", "value": "2"}, {"key": "Bundle-Name", "value": "unbescape"}, {"key": "Bundle-SymbolicName", "value": "org.unbescape"}, {"key": "Bundle-Vendor", "value": "The UNBESCAPE team"}, {"key": "Bundle-Version", "value": "1.1.6.RELEASE"}, {"key": "Export-Package", "value": "org.unbescape.uri;version=\"1.1.6\",org.unbescape;version=\"1.1.6\",org.unbescape.javascript;version=\"1.1.6\",org.unbescape.css;version=\"1.1.6\",org.unbescape.csv;version=\"1.1.6\",org.unbescape.json;version=\"1.1.6\",org.unbescape.xml;version=\"1.1.6\",org.unbescape.java;version=\"1.1.6\",org.unbescape.html;version=\"1.1.6\",org.unbescape.properties;version=\"1.1.6\""}, {"key": "Require-Capability", "value": "osgi.ee;filter:=\"(&(osgi.ee=JavaSE)(version=1.6))\""}, {"key": "Tool", "value": "Bnd-3.5.0.201709291849"}]}, "digest": [{"algorithm": "sha1", "value": "7b90360afb2b860e09e8347112800d12c12b2a13"}]}}], "artifactRelationships": [{"parent": "0344c250a9ed24c4", "child": "55435742bf8f6fe5", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "064b8f8c2b61b25a", "child": "56840266f6c0f445", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "0753aa97cfe8bef4", "child": "0328106cb52bb1b0", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "0753aa97cfe8bef4", "child": "eaf71ad5b5315431", "type": "dependency-of"}, {"parent": "09ee2d790acd922d", "child": "0328106cb52bb1b0", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "09ee2d790acd922d", "child": "eaf71ad5b5315431", "type": "dependency-of"}, {"parent": "1eb294e895425f78", "child": "0328106cb52bb1b0", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "1eb294e895425f78", "child": "eaf71ad5b5315431", "type": "dependency-of"}, {"parent": "2bfc9bfc01d42dd5", "child": "df5658b32a53b0dd", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "2d9a8e13f9915139", "child": "0328106cb52bb1b0", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "2d9a8e13f9915139", "child": "eaf71ad5b5315431", "type": "dependency-of"}, {"parent": "34070c4539bb7594", "child": "705fe9469f9bbd04", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "35ee804b8df916dd", "child": "0328106cb52bb1b0", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "35ee804b8df916dd", "child": "eaf71ad5b5315431", "type": "dependency-of"}, {"parent": "4fcb6ead1ed74d0a", "child": "d698282807d3b7ac", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "500585d6df9f3ad7", "child": "1bcb5fed9b777f2d", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "62674903d1839d81", "child": "0328106cb52bb1b0", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "62674903d1839d81", "child": "eaf71ad5b5315431", "type": "dependency-of"}, {"parent": "64d975dbc03b4865", "child": "5efa9c11733aaf2a", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "70e7d1ed520e8a9d", "child": "0328106cb52bb1b0", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "70e7d1ed520e8a9d", "child": "eaf71ad5b5315431", "type": "dependency-of"}, {"parent": "822e5bf35009365f", "child": "efcfceeb0d5d3088", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "839d1f5093f2747d", "child": "1960867ec558430a", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "90f49a2feacd69a0", "child": "c12d3c56403ab9ea", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "a00383392aa9ea47", "child": "a450ad3a8aa2ffe8", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "a36cb0d400bcb22b", "child": "7ace6655b19e5473", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "ab66f9a093724bd0", "child": "b775520b5548ea5a", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "ae5877e5630935d9", "child": "0328106cb52bb1b0", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "ae5877e5630935d9", "child": "eaf71ad5b5315431", "type": "dependency-of"}, {"parent": "aeda88acf8f42219", "child": "a5794b97b258e624", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "afb0085e06d1bb0b", "child": "07394d99cb7f4064", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "bed203887412f6b5", "child": "0328106cb52bb1b0", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "bed203887412f6b5", "child": "eaf71ad5b5315431", "type": "dependency-of"}, {"parent": "c4d4517469d619be", "child": "0328106cb52bb1b0", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "c4d4517469d619be", "child": "eaf71ad5b5315431", "type": "dependency-of"}, {"parent": "cb58912cd5a6c980", "child": "ad85d3662a64a295", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "0344c250a9ed24c4", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "064b8f8c2b61b25a", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "0753aa97cfe8bef4", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "09ee2d790acd922d", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "1eb294e895425f78", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "2bfc9bfc01d42dd5", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "2d9a8e13f9915139", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "34070c4539bb7594", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "35ee804b8df916dd", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "4fcb6ead1ed74d0a", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "500585d6df9f3ad7", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "62674903d1839d81", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "64d975dbc03b4865", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "70e7d1ed520e8a9d", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "822e5bf35009365f", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "839d1f5093f2747d", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "90f49a2feacd69a0", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "a00383392aa9ea47", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "a36cb0d400bcb22b", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "ab66f9a093724bd0", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "ae5877e5630935d9", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "aeda88acf8f42219", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "afb0085e06d1bb0b", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "bed203887412f6b5", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "c4d4517469d619be", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "cb58912cd5a6c980", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "d3877657428d7f33", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "e9d85faf74fc124a", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "eaf71ad5b5315431", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "eb228c50d7df59b7", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "ed580897c4ca2eb5", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "eec6db8941e5b28e", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "ef47a662b1676ece", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "f9c94850d7f61da8", "type": "contains"}, {"parent": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "child": "fa3a958a5056b332", "type": "contains"}, {"parent": "d3877657428d7f33", "child": "0328106cb52bb1b0", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "d3877657428d7f33", "child": "eaf71ad5b5315431", "type": "dependency-of"}, {"parent": "e9d85faf74fc124a", "child": "1eda86386ef8a2f1", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "eaf71ad5b5315431", "child": "0328106cb52bb1b0", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "eb228c50d7df59b7", "child": "bba2b51dff895ec3", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "ed580897c4ca2eb5", "child": "6ea5dd301a0f0c12", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "eec6db8941e5b28e", "child": "0328106cb52bb1b0", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "eec6db8941e5b28e", "child": "eaf71ad5b5315431", "type": "dependency-of"}, {"parent": "ef47a662b1676ece", "child": "0328106cb52bb1b0", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "ef47a662b1676ece", "child": "eaf71ad5b5315431", "type": "dependency-of"}, {"parent": "f9c94850d7f61da8", "child": "46d50407ac98eed0", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "fa3a958a5056b332", "child": "0328106cb52bb1b0", "type": "evident-by", "metadata": {"kind": "primary"}}, {"parent": "fa3a958a5056b332", "child": "eaf71ad5b5315431", "type": "dependency-of"}], "files": [{"id": "547f9340a8021aca", "location": {"path": "/build/WEB-INF/lib/servlet-api.jar"}, "unknowns": ["java-archive-cataloger: unable to read files from java archive: zip: not a valid zip file"]}, {"id": "fc38361f453e6463", "location": {"path": "/lib/servlet-api.jar"}, "unknowns": ["java-archive-cataloger: unable to read files from java archive: zip: not a valid zip file"]}, {"id": "0328106cb52bb1b0", "location": {"path": "/pom.xml"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "text/xml", "size": 6419}, "digests": [{"algorithm": "sha1", "value": "97f98e9637e4ffc66f8d9d98cfbbcc23ef910e05"}, {"algorithm": "sha256", "value": "89c19c42cedc8a3a30554ecf3906ebd72e5b23c51ab6a1bc71ce526b926e40e4"}]}, {"id": "a450ad3a8aa2ffe8", "location": {"path": "/target/dependency/attoparser-2.0.5.RELEASE.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/jar", "size": 244959}, "digests": [{"algorithm": "sha1", "value": "a93ad36df9560de3a5312c1d14f69d938099fa64"}, {"algorithm": "sha256", "value": "d4015d56147f696ed0a90078675bc940529f907e7b2dfc1fad754e8033da8796"}]}, {"id": "6ea5dd301a0f0c12", "location": {"path": "/target/dependency/commons-lang3-3.9.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/jar", "size": 503880}, "digests": [{"algorithm": "sha1", "value": "0122c7cee69b53ed4a7681c03d4ee4c0e2765da5"}, {"algorithm": "sha256", "value": "de2e1dcdcf3ef917a8ce858661a06726a9a944f28e33ad7f9e08bea44dc3c230"}]}, {"id": "ad85d3662a64a295", "location": {"path": "/target/dependency/gson-2.8.6.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/zip", "size": 240255}, "digests": [{"algorithm": "sha1", "value": "9180733b7df8542621dc12e21e87557e8c99b8cb"}, {"algorithm": "sha256", "value": "c8fb4839054d280b3033f800d1f5a97de2f028eb8ba2eb458ad287e536f3f25f"}]}, {"id": "07394d99cb7f4064", "location": {"path": "/target/dependency/hamcrest-core-1.3.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/zip", "size": 45024}, "digests": [{"algorithm": "sha1", "value": "42a25dc3219429f0e5d060061f71acb49bf010a0"}, {"algorithm": "sha256", "value": "66fdef91e9739348df7a096aa384a5685f4e875584cce89386a7a47251c4d8e9"}]}, {"id": "bba2b51dff895ec3", "location": {"path": "/target/dependency/jackson-annotations-2.9.10.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/jar", "size": 66894}, "digests": [{"algorithm": "sha1", "value": "53ab2f0f92e87ea4874c8c6997335c211d81e636"}, {"algorithm": "sha256", "value": "c876f2e85d0f108a34cdd11ccc9d8d7875697367efc75bf10a89c2c26aee994c"}]}, {"id": "a5794b97b258e624", "location": {"path": "/target/dependency/jackson-core-2.9.10.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/jar", "size": 325636}, "digests": [{"algorithm": "sha1", "value": "66b715dec9dd8b0f39f3296e67e05913bf422d0c"}, {"algorithm": "sha256", "value": "65fe26d7554a4409652c86ee38f2e94bc42934326d88b3c78c61f66ff2222c53"}]}, {"id": "d698282807d3b7ac", "location": {"path": "/target/dependency/jackson-databind-2.9.10.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/jar", "size": 1348567}, "digests": [{"algorithm": "sha1", "value": "e201bb70b7469ba18dd58ed8268aa44e702fa2f0"}, {"algorithm": "sha256", "value": "49bb71a73fcdcdf59c40a1a01d7245f41d3a8ba96ea6182b720f0c6167241757"}]}, {"id": "46d50407ac98eed0", "location": {"path": "/target/dependency/javassist-3.20.0-GA.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/jar", "size": 750581}, "digests": [{"algorithm": "sha1", "value": "a9cbcdfb7e9f86fbc74d3afae65f2248bfbf82a0"}, {"algorithm": "sha256", "value": "d7691062fb779c2381640c8f72acba2c23873b01c243866d41c15dc4c8848ea2"}]}, {"id": "7ace6655b19e5473", "location": {"path": "/target/dependency/javax.servlet-api-3.1.0.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/zip", "size": 95806}, "digests": [{"algorithm": "sha1", "value": "3cd63d075497751784b2fa84be59432f4905bf7c"}, {"algorithm": "sha256", "value": "af456b2dd41c4e82cf54f3e743bc678973d9fe35bd4d3071fa05c7e5333b8482"}]}, {"id": "1bcb5fed9b777f2d", "location": {"path": "/target/dependency/javax.servlet.jsp-api-2.3.3.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/jar", "size": 54200}, "digests": [{"algorithm": "sha1", "value": "81191ab80e342912dc9cea735c30ff4eddc64de3"}, {"algorithm": "sha256", "value": "409a534d275ef0958a2c1692472da30e3706bfe6933d56c039376f53f13689b7"}]}, {"id": "1960867ec558430a", "location": {"path": "/target/dependency/json-simple-1.1.1.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/jar", "size": 23931}, "digests": [{"algorithm": "sha1", "value": "c9ad4a0850ab676c5c64461a05ca524cdfff59f1"}, {"algorithm": "sha256", "value": "4e69696892b88b41c55d49ab2fdcc21eead92bf54acc588c0050596c3b75199c"}]}, {"id": "1eda86386ef8a2f1", "location": {"path": "/target/dependency/junit-4.12.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/zip", "size": 314932}, "digests": [{"algorithm": "sha1", "value": "2973d150c0dc1fefe998f834810d68f278ea58ec"}, {"algorithm": "sha256", "value": "59721f0805e223d84b90677887d9ff567dc534d7c502ca903c0c2b17f05c116a"}]}, {"id": "5efa9c11733aaf2a", "location": {"path": "/target/dependency/logback-classic-1.2.3.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/zip", "size": 290339}, "digests": [{"algorithm": "sha1", "value": "7c4f3c474fb2c041d8028740440937705ebb473a"}, {"algorithm": "sha256", "value": "fb53f8539e7fcb8f093a56e138112056ec1dc809ebb020b59d8a36a5ebac37e0"}]}, {"id": "df5658b32a53b0dd", "location": {"path": "/target/dependency/logback-core-1.2.3.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/zip", "size": 471901}, "digests": [{"algorithm": "sha1", "value": "864344400c3d4d92dfeb0a305dc87d953677c03c"}, {"algorithm": "sha256", "value": "5946d837fe6f960c02a53eda7a6926ecc3c758bbdd69aa453ee429f858217f22"}]}, {"id": "b775520b5548ea5a", "location": {"path": "/target/dependency/ognl-3.1.26.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/zip", "size": 262229}, "digests": [{"algorithm": "sha1", "value": "922d3d922b8aa40146d842114c184c8b403d2f4f"}, {"algorithm": "sha256", "value": "807277276d4b9d5231139ad50885995ba1a6631498e6c1cf61e6e156d15872f5"}]}, {"id": "705fe9469f9bbd04", "location": {"path": "/target/dependency/slf4j-api-1.7.30.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/zip", "size": 41472}, "digests": [{"algorithm": "sha1", "value": "b5a4b6d16ab13e34a88fae84c35cd5d68cac922c"}, {"algorithm": "sha256", "value": "cdba07964d1bb40a0761485c6b1e8c2f8fd9eb1d19c53928ac0d7f9510105c57"}]}, {"id": "c12d3c56403ab9ea", "location": {"path": "/target/dependency/thymeleaf-3.0.15.RELEASE.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/jar", "size": 870892}, "digests": [{"algorithm": "sha1", "value": "13e3296a03d8a597b734d832ed8656139bf9cdd8"}, {"algorithm": "sha256", "value": "9ea400b29d938eec670684eaa706a4fc11cd6c6ba58fd39b8ea9e13cb0177d75"}]}, {"id": "efcfceeb0d5d3088", "location": {"path": "/target/dependency/thymeleaf-extras-java8time-3.0.4.RELEASE.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/jar", "size": 40092}, "digests": [{"algorithm": "sha1", "value": "36e7175ddce36c486fff4578b5af7bb32f54f5df"}, {"algorithm": "sha256", "value": "c07690c764329afd148a4134980d636911390a3fda45f6c6ae46517e4b4444d3"}]}, {"id": "56840266f6c0f445", "location": {"path": "/target/dependency/thymeleaf-spring5-3.0.15.RELEASE.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/jar", "size": 182002}, "digests": [{"algorithm": "sha1", "value": "7170e1bcd1588d38c139f7048ebcc262676441c3"}, {"algorithm": "sha256", "value": "d254da927435a517cfa1a5aed5b27b74818418981ef3b816e8da89e4390b52d5"}]}, {"id": "55435742bf8f6fe5", "location": {"path": "/target/dependency/unbescape-1.1.6.RELEASE.jar"}, "metadata": {"mode": 644, "type": "RegularFile", "userID": 0, "groupID": 0, "mimeType": "application/jar", "size": 173935}, "digests": [{"algorithm": "sha1", "value": "7b90360afb2b860e09e8347112800d12c12b2a13"}, {"algorithm": "sha256", "value": "597cf87d5b1a4f385b9d1cec974b7b483abb3ee85fc5b3f8b62af8e4bec95c2c"}]}], "source": {"id": "cdb4ee2aea69cc6a83331bbe96dc2caa9a299d21329efb0336fc02a82e1839a8", "name": ".", "version": "", "type": "directory", "metadata": {"path": "."}}, "distro": {}, "descriptor": {"name": "syft", "version": "1.31.0", "configuration": {"catalogers": {"requested": {"default": ["directory", "file"]}, "used": ["alpm-db-cataloger", "apk-db-cataloger", "binary-classifier-cataloger", "cargo-auditable-binary-cataloger", "cocoapods-cataloger", "conan-cataloger", "dart-pubspec-cataloger", "dart-pubspec-lock-cataloger", "deb-archive-cataloger", "dotnet-deps-binary-cataloger", "dotnet-packages-lock-cataloger", "dpkg-db-cataloger", "elf-binary-package-cataloger", "elixir-mix-lock-cataloger", "erlang-otp-application-cataloger", "erlang-rebar-lock-cataloger", "file-content-cataloger", "file-digest-cataloger", "file-executable-cataloger", "file-metadata-cataloger", "github-action-workflow-usage-cataloger", "github-actions-usage-cataloger", "go-module-binary-cataloger", "go-module-file-cataloger", "graalvm-native-image-cataloger", "haskell-cataloger", "homebrew-cataloger", "java-archive-cataloger", "java-gradle-lockfile-cataloger", "java-jvm-cataloger", "java-pom-cataloger", "javascript-lock-cataloger", "linux-kernel-cataloger", "lua-rock-cataloger", "nix-cataloger", "opam-cataloger", "pe-binary-package-cataloger", "php-composer-lock-cataloger", "php-interpreter-cataloger", "php-pear-serialized-cataloger", "portage-cataloger", "python-installed-package-cataloger", "python-package-cataloger", "r-package-cataloger", "rpm-archive-cataloger", "rpm-db-cataloger", "ruby-gemfile-cataloger", "ruby-gemspec-cataloger", "rust-cargo-lock-cataloger", "swift-package-manager-cataloger", "swipl-pack-cataloger", "terraform-lock-cataloger", "wordpress-plugins-cataloger"]}, "data-generation": {"generate-cpes": true}, "files": {"content": {"globs": null, "skip-files-above-size": 0}, "hashers": ["sha-1", "sha-256"], "selection": "owned-by-package"}, "licenses": {"coverage": 75, "include-content": "none"}, "packages": {"binary": ["python-binary", "python-binary-lib", "pypy-binary-lib", "go-binary", "julia-binary", "helm", "redis-binary", "nodejs-binary", "go-binary-hint", "busybox-binary", "util-linux-binary", "haproxy-binary", "perl-binary", "php-composer-binary", "httpd-binary", "memcached-binary", "traefik-binary", "arangodb-binary", "postgresql-binary", "mysql-binary", "mysql-binary", "mysql-binary", "xtrabackup-binary", "mariadb-binary", "rust-standard-library-linux", "rust-standard-library-macos", "ruby-binary", "erlang-binary", "erlang-alpine-binary", "erlang-library", "swipl-binary", "dart-binary", "haskell-ghc-binary", "haskell-cabal-binary", "haskell-stack-binary", "consul-binary", "hashicorp-vault-binary", "nginx-binary", "bash-binary", "openssl-binary", "gcc-binary", "fluent-bit-binary", "wordpress-cli-binary", "curl-binary", "lighttpd-binary", "proftpd-binary", "zstd-binary", "xz-binary", "gzip-binary", "sqlcipher-binary", "jq-binary", "chrome-binary", "java-binary", "java-jdb-binary"], "dotnet": {"dep-packages-must-claim-dll": true, "dep-packages-must-have-dll": false, "propagate-dll-claims-to-parents": true, "relax-dll-claims-when-bundling-detected": true}, "golang": {"local-mod-cache-dir": "/root/go/pkg/mod", "local-vendor-dir": "", "main-module-version": {"from-build-settings": true, "from-contents": false, "from-ld-flags": true}, "proxies": ["https://proxy.golang.org", "direct"], "search-local-mod-cache-licenses": false, "search-local-vendor-licenses": false, "search-remote-licenses": false}, "java-archive": {"include-indexed-archives": true, "include-unindexed-archives": false, "maven-base-url": "https://repo1.maven.org/maven2", "maven-localrepository-dir": "/root/.m2/repository", "max-parent-recursive-depth": 0, "resolve-transitive-dependencies": false, "use-maven-localrepository": false, "use-network": false}, "javascript": {"include-dev-dependencies": false, "npm-base-url": "https://registry.npmjs.org", "search-remote-licenses": false}, "linux-kernel": {"catalog-modules": true}, "nix": {"capture-owned-files": false}, "python": {"guess-unpinned-requirements": false}}, "relationships": {"exclude-binary-packages-with-file-ownership-overlap": true, "package-file-ownership": true, "package-file-ownership-overlap": true}, "search": {"scope": "squashed"}}}, "schema": {"version": "16.0.37", "url": "https://raw.githubusercontent.com/anchore/syft/main/schema/json/schema-16.0.37.json"}}