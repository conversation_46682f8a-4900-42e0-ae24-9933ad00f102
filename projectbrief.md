# 项目概要文档

## 项目名称
moonbit-augment-vip - VS Code Cleaner Tool

## 核心需求与目标
开发一个使用MoonBit语言的VS Code数据库清理和遥测ID修改工具，通过LLVM后端生成高性能的原生可执行文件。

### 主要功能需求
1. **数据库清理功能** - 自动检测并清理VS Code数据库中包含"augment"的条目
2. **遥测ID修改功能** - 生成新的机器ID和设备ID，更新VS Code配置
3. **批量操作功能** - 一次性执行所有清理和修改操作

### 技术目标
- 使用MoonBit语言开发，利用LLVM后端生成优化的原生机器码
- 通过FFI集成C库实现SQLite数据库操作和系统API调用
- 支持Windows、macOS、Linux三大操作系统
- 生成零依赖的独立可执行文件
- 采用手动构建方式，不依赖自动化脚本

## 项目范围
- **包含**：VS Code数据库清理、遥测ID修改、跨平台支持、命令行界面
- **不包含**：GUI界面、其他编辑器支持、网络功能

## 成功标准
1. 能够正确识别和清理VS Code数据库中的特定条目
2. 能够安全地修改VS Code的遥测配置
3. 在三大主流操作系统上正常运行
4. 生成的可执行文件无需额外依赖
5. 提供清晰的命令行界面和操作反馈

## 项目约束
- 必须使用MoonBit语言开发
- 必须通过LLVM后端编译
- 必须确保内存安全和类型安全
- 必须支持跨平台运行
