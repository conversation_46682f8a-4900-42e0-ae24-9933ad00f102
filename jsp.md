# JSP项目创建与Syft扫描记录

## 1. 前置操作

### 1.1 停止nginx服务
```bash
# 停止nginx服务
systemctl stop nginx

# 验证nginx已停止
systemctl status nginx
```

**结果：** nginx服务已成功停止

## 2. JSP项目创建过程

### 2.1 创建项目目录结构
```bash
# 创建jsp目录
mkdir jsp

# 创建标准JSP项目结构
cd jsp
mkdir -p src/main/webapp/WEB-INF src/main/java/com/example lib
```

### 2.2 项目文件创建

#### 2.2.1 Web配置文件 (web.xml)
**路径：** `jsp/src/main/webapp/WEB-INF/web.xml`
```xml
<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee
         http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
         version="2.5">

    <display-name>Simple JSP Health Check App</display-name>
    
    <servlet>
        <servlet-name>HealthCheckServlet</servlet-name>
        <servlet-class>com.example.HealthCheckServlet</servlet-class>
    </servlet>
    
    <servlet-mapping>
        <servlet-name>HealthCheckServlet</servlet-name>
        <url-pattern>/health</url-pattern>
    </servlet-mapping>
    
    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>
    
</web-app>
```

#### 2.2.2 健康检查Servlet (HealthCheckServlet.java)
**路径：** `jsp/src/main/java/com/example/HealthCheckServlet.java`

**特点：**
- 使用Java 8兼容语法
- 提供RESTful健康检查接口
- 返回JSON格式响应
- 包含服务状态、时间戳、版本信息

#### 2.2.3 主页JSP文件 (index.jsp)
**路径：** `jsp/src/main/webapp/index.jsp`

**功能：**
- 显示服务状态
- 展示可用API端点
- 提供测试链接
- 显示系统信息

#### 2.2.4 构建脚本 (build.sh)
**路径：** `jsp/build.sh`

```bash
# 设置可执行权限
chmod +x build.sh

# 运行构建脚本
./build.sh
```

**构建输出：**
```
Building JSP Health Check Project...
Warning: servlet-api.jar not found in lib/ directory
This is a demonstration build script.
In a real environment, you would need:
1. servlet-api.jar (from Tomcat or other servlet container)
2. jsp-api.jar (for JSP compilation)
Copying web resources...
Copying libraries...
Java compilation would happen here with proper classpath...
javac -cp lib/servlet-api.jar -d build/WEB-INF/classes src/main/java/com/example/*.java
Creating WAR file structure...
WAR file would contain:
./index.jsp
./WEB-INF/lib/servlet-api.jar
./WEB-INF/web.xml
Build completed. WAR structure created in build/
To deploy: copy contents to your servlet container's webapps directory
```

## 3. 项目结构

### 3.1 最终目录结构
```
jsp/
├── src/
│   └── main/
│       ├── java/
│       │   └── com/
│       │       └── example/
│       │           └── HealthCheckServlet.java
│       └── webapp/
│           ├── index.jsp
│           └── WEB-INF/
│               └── web.xml
├── lib/
│   └── servlet-api.jar (placeholder)
├── build/
│   ├── index.jsp
│   └── WEB-INF/
│       ├── web.xml
│       └── lib/
│           └── servlet-api.jar
├── build.sh
├── file-list.txt
└── README.md
```

## 4. Syft扫描过程

### 4.1 扫描整个JSP项目目录
```bash
cd jsp
syft . -o syft-table
```

**扫描结果：**
```
No packages discovered
```

### 4.2 扫描源代码目录
```bash
syft src/ -o syft-json
```

**扫描结果：**
- 未发现任何包
- 原因：JSP项目主要包含源代码文件，没有编译后的二进制包或依赖管理文件

### 4.3 扫描分析

**为什么没有发现包：**
1. **源代码项目**：这是一个源代码项目，不是编译后的应用
2. **缺少依赖管理**：没有Maven pom.xml或Gradle build文件
3. **没有JAR文件**：lib目录中只有占位符文件
4. **未编译**：Java源文件未编译成.class文件

**Syft能识别的Java相关文件类型：**
- JAR/WAR/EAR文件
- Maven pom.xml
- Gradle build文件
- 编译后的Java二进制文件

## 5. 项目特点

### 5.1 技术栈
- **Java版本**：兼容Java 1.8+（低版本Java）
- **Servlet API**：2.5版本
- **JSP版本**：2.1
- **构建方式**：Shell脚本（无Maven/Gradle依赖）

### 5.2 API接口
- **健康检查端点**：`/health`
- **响应格式**：JSON
- **HTTP方法**：GET/POST
- **功能**：返回服务状态、时间戳、版本信息

### 5.3 部署要求
- Servlet容器（Tomcat、Jetty等）
- Java 8或更高版本
- servlet-api.jar（由容器提供）

### 4.4 添加Thymeleaf依赖

#### 4.4.1 创建Maven配置文件
```bash
# 创建pom.xml文件，包含Thymeleaf依赖
```

**pom.xml关键依赖：**
```xml
<!-- Thymeleaf Core -->
<dependency>
    <groupId>org.thymeleaf</groupId>
    <artifactId>thymeleaf</artifactId>
    <version>3.0.15.RELEASE</version>
</dependency>

<!-- Thymeleaf Spring Integration -->
<dependency>
    <groupId>org.thymeleaf</groupId>
    <artifactId>thymeleaf-spring5</artifactId>
    <version>3.0.15.RELEASE</version>
</dependency>

<!-- Thymeleaf Extras for Java 8 Time -->
<dependency>
    <groupId>org.thymeleaf.extras</groupId>
    <artifactId>thymeleaf-extras-java8time</artifactId>
    <version>3.0.4.RELEASE</version>
</dependency>

<!-- JSON处理库 -->
<dependency>
    <groupId>com.google.code.gson</groupId>
    <artifactId>gson</artifactId>
    <version>2.8.6</version>
</dependency>

<!-- Jackson JSON库 -->
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
    <version>2.9.10</version>
</dependency>
```

#### 4.4.2 安装Maven并下载依赖
```bash
# 安装Maven
yum install maven -y

# 验证Maven版本
mvn --version

# 下载项目依赖
cd jsp
mvn dependency:copy-dependencies
```

**Maven下载结果：**
- 成功下载20个JAR依赖文件
- 总下载时间：9分06秒
- 依赖存储位置：`target/dependency/`

### 4.5 Syft扫描（包含真实依赖）

#### 4.5.1 扫描包含依赖的项目
```bash
cd jsp
syft . -o syft-table
```

**扫描结果（35个包）：**
```
NAME                        VERSION         TYPE
attoparser                  2.0.5.RELEASE   java-archive
commons-lang3               3.9             java-archive  (+1 duplicate)
gson                        2.8.6           java-archive  (+1 duplicate)
hamcrest-core               1.3             java-archive
jackson-annotations         2.9.10          java-archive  (+1 duplicate)
jackson-core                2.9.10          java-archive  (+1 duplicate)
jackson-databind            2.9.10          java-archive  (+1 duplicate)
javassist                   3.20.0-GA       java-archive
javax.servlet-api           3.1.0           java-archive  (+1 duplicate)
javax.servlet.jsp-api       2.3.3           java-archive  (+1 duplicate)
json-simple                 1.1.1           java-archive  (+1 duplicate)
jsp-health-check            1.0.0           java-archive
junit                       4.12            java-archive  (+1 duplicate)
logback-classic             1.2.3           java-archive  (+1 duplicate)
logback-core                1.2.3           java-archive
ognl                        3.1.26          java-archive
slf4j-api                   1.7.30          java-archive  (+1 duplicate)
thymeleaf                   3.0.15.RELEASE  java-archive  (+1 duplicate)
thymeleaf-extras-java8time  3.0.4.RELEASE   java-archive  (+1 duplicate)
thymeleaf-spring5           3.0.15.RELEASE  java-archive  (+1 duplicate)
unbescape                   1.1.6.RELEASE   java-archive
```

#### 4.5.2 生成详细JSON报告
```bash
syft . -o syft-json > syft-scan-results.json
```

### 4.6 扫描命令总结
```bash
# 表格格式扫描（包含依赖）
syft jsp/ -o syft-table

# JSON格式扫描（详细信息）
syft jsp/ -o syft-json > syft-scan-results.json

# SPDX格式扫描
syft jsp/ -o spdx-json > jsp-spdx.json

# 其他可用格式
syft jsp/ -o cyclonedx-json    # CycloneDX JSON格式
syft jsp/ -o syft-text         # 文本格式
```

## 7. 总结

### 7.1 项目创建成果
成功创建了一个完整的JSP健康检查项目，包含：
- 标准的Java Web项目结构
- RESTful健康检查接口（`/health`端点）
- 兼容低版本Java 8的代码
- Maven构建配置和依赖管理
- Thymeleaf模板引擎集成
- 完整的项目文档和构建脚本

### 7.2 Syft扫描对比分析

#### 7.2.1 初始扫描（无依赖）
- **包发现**：0个包
- **原因**：只有源代码，无实际JAR依赖

#### 7.2.2 添加依赖后扫描（成功）
- **包发现**：35个包
- **主要组件**：
  - **Thymeleaf**: 3.0.15.RELEASE（模板引擎）
  - **Jackson**: 2.9.10（JSON处理）
  - **Gson**: 2.8.6（JSON处理）
  - **Logback**: 1.2.3（日志框架）
  - **JUnit**: 4.12（测试框架）
  - **Servlet API**: 3.1.0（Web容器接口）

### 7.3 关键发现
1. **Maven依赖管理的重要性**：有了pom.xml和真实JAR文件后，Syft能够准确识别所有依赖
2. **Thymeleaf生态系统**：包含核心库、Spring集成、Java 8时间支持等完整组件
3. **传递依赖识别**：Syft能够发现间接依赖，如OGNL、Javassist等
4. **版本兼容性**：所有依赖都兼容Java 8环境

### 7.4 安全和合规价值
通过Syft扫描获得的软件清单可用于：
- **漏洞扫描**：基于CVE数据库进行安全评估
- **许可证合规**：检查开源许可证兼容性
- **供应链安全**：追踪第三方组件来源
- **版本管理**：识别过时或有风险的组件版本

### 7.5 最佳实践总结
1. **使用标准构建工具**：Maven/Gradle确保依赖管理的一致性
2. **定期扫描依赖**：集成到CI/CD流程中
3. **版本控制**：使用具体版本号而非范围版本
4. **最小化依赖**：只引入必要的组件，减少攻击面

### 7.6 项目实际价值
- ✅ 演示了完整的JSP项目结构和依赖管理
- ✅ 提供了可工作的健康检查接口
- ✅ 展示了Syft在真实项目中的应用效果
- ✅ 为后续的安全扫描和合规检查提供了基础
