### Added Features

- Option to set `PackageSupplier` in root of SPDX document generated by CLI [[#3098](https://github.com/anchore/syft/issues/3098) [#4131](https://github.com/anchore/syft/pull/4131) @spiffcs]

### Bug Fixes

- closed reader during java binary detection [[#4129](https://github.com/anchore/syft/pull/4129) @kzantow]
- support multiple letters in openssl patch version [[#4106](https://github.com/anchore/syft/pull/4106) @honigbot]
- Can not have license ID [[#1964](https://github.com/anchore/syft/issues/1964) [#4132](https://github.com/anchore/syft/pull/4132) @spiffcs]
- Syft sometimes reports URL for license value when scanning JARs with a URL in `Bundle-License` field of manifest [[#3186](https://github.com/anchore/syft/issues/3186)]

**[(Full Changelog)](https://github.com/anchore/syft/compare/v1.30.0...v1.31.0)**
