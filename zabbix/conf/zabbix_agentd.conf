# Zabbix Agent Configuration File

# 基本配置
PidFile=/var/run/zabbix/zabbix_agentd.pid
LogType=console
LogLevel=3
DebugLevel=3

# 服务器配置
Server=zabbix-server
ServerActive=zabbix-server:10051
Hostname=Zabbix server

# 网络配置
ListenPort=10050
ListenIP=0.0.0.0

# 性能配置
StartAgents=3
RefreshActiveChecks=120
BufferSend=5
BufferSize=100
MaxLinesPerSecond=20

# 超时配置
Timeout=3

# 安全配置
AllowRoot=0
User=zabbix

# 用户参数配置
# UserParameter=custom.key,command

# 包含其他配置文件
# Include=/etc/zabbix/zabbix_agentd.d/*.conf

# 不安全的用户参数（仅用于测试）
UnsafeUserParameters=0

# TLS配置（可选）
# TLSConnect=unencrypted
# TLSAccept=unencrypted

# 主动检查配置
EnableRemoteCommands=0
LogRemoteCommands=0

# 系统监控配置
LoadModulePath=/var/lib/zabbix/modules

# 自定义监控项示例
UserParameter=custom.cpu.discovery,echo '{"data":[{"{#CPU}":"cpu0"},{"{#CPU}":"cpu1"}]}'
UserParameter=custom.memory.usage,free | grep Mem | awk '{printf "%.2f", $3/$2 * 100.0}'
UserParameter=custom.disk.usage[*],df -h $1 | tail -1 | awk '{print $5}' | sed 's/%//'
UserParameter=custom.network.bytes[*],cat /proc/net/dev | grep $1 | awk '{print $2}'

# 进程监控
UserParameter=proc.count[*],ps aux | grep -c "$1"
UserParameter=proc.memory[*],ps aux | grep "$1" | awk '{sum+=$6} END {print sum}'

# 服务状态检查
UserParameter=service.status[*],systemctl is-active $1 && echo 1 || echo 0

# 日志监控
UserParameter=log.count[*],grep -c "$2" $1 2>/dev/null || echo 0

# 网络连接监控
UserParameter=net.tcp.listen[*],netstat -tlnp | grep ":$1 " | wc -l
UserParameter=net.tcp.established[*],netstat -tnp | grep ":$1 " | grep ESTABLISHED | wc -l
