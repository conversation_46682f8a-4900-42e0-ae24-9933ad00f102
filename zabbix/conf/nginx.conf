user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 16M;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Zabbix Web配置
    server {
        listen 8080;
        server_name _;
        
        root /usr/share/zabbix;
        index index.php index.html index.htm;

        # 安全头
        add_header X-Frame-Options SAMEORIGIN;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";

        # PHP配置
        location ~ \.php$ {
            fastcgi_pass unix:/var/run/php-fpm.sock;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            include fastcgi_params;
            fastcgi_param PHP_VALUE "
                max_execution_time = 300
                memory_limit = 128M
                post_max_size = 16M
                upload_max_filesize = 2M
                max_input_time = 300
                max_input_vars = 10000
                date.timezone = Asia/Shanghai
            ";
        }

        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # 拒绝访问敏感文件
        location ~ /\. {
            deny all;
        }

        location ~ /(conf|app|include)/ {
            deny all;
        }

        # API接口
        location ~ ^/api/ {
            try_files $uri $uri/ /api_jsonrpc.php?$args;
        }

        # 默认location
        location / {
            try_files $uri $uri/ /index.php?$args;
        }

        # 健康检查
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
