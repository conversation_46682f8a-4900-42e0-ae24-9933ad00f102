# Zabbix Server Configuration File

# 数据库配置
DBHost=mysql-server
DBName=zabbix
DBUser=zabbix
DBPassword=zabbix_pwd
DBPort=3306

# 服务器配置
LogType=console
LogLevel=3
PidFile=/var/run/zabbix/zabbix_server.pid
SocketDir=/var/run/zabbix

# 网络配置
ListenPort=10051
ListenIP=0.0.0.0

# 性能调优
StartPollers=5
StartPollersUnreachable=1
StartTrappers=5
StartPingers=1
StartDiscoverers=1
StartHTTPPollers=1
StartTimers=1
StartEscalators=1
StartAlerters=3

# Java Gateway配置
JavaGateway=zabbix-java-gateway
JavaGatewayPort=10052
StartJavaPollers=2

# 缓存配置
CacheSize=8M
HistoryCacheSize=16M
HistoryIndexCacheSize=4M
TrendCacheSize=4M
ValueCacheSize=8M

# 超时配置
Timeout=4
TrapperTimeout=300
UnreachablePeriod=45
UnavailableDelay=60
UnreachableDelay=15

# 日志配置
LogSlowQueries=3000
StatsAllowedIP=127.0.0.1

# 安全配置
AllowRoot=0

# SNMP配置
SNMPTrapperFile=/var/log/snmptrap/snmptrap.log
StartSNMPTrapper=0

# 外部脚本配置
ExternalScripts=/usr/lib/zabbix/externalscripts
FpingLocation=/usr/bin/fping
Fping6Location=/usr/bin/fping6

# 告警脚本配置
AlertScriptsPath=/usr/lib/zabbix/alertscripts

# 临时文件配置
TmpDir=/tmp

# 启动配置
StartVMwareCollectors=0
VMwareFrequency=60
VMwarePerfFrequency=60
VMwareCacheSize=8M
VMwareTimeout=10

# Web服务配置
StartLLDProcessors=2
StatsAllowedIP=127.0.0.1

# 历史数据配置
HousekeepingFrequency=1
MaxHousekeeperDelete=5000
SenderFrequency=30

# 代理配置
ProxyConfigFrequency=3600
ProxyDataFrequency=1

# 负载均衡配置
LoadModulePath=/var/lib/zabbix/modules
