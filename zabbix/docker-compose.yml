version: '3.8'

services:
  # MySQL数据库
  mysql-server:
    image: mysql:8.0
    container_name: zabbix-mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: zabbix
      MYSQL_USER: zabbix
      MYSQL_PASSWORD: zabbix_pwd
      MYSQL_ROOT_PASSWORD: root_pwd
    command:
      - mysqld
      - --character-set-server=utf8
      - --collation-server=utf8_bin
      - --default-authentication-plugin=mysql_native_password
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - zabbix-net

  # Zabbix Server
  zabbix-server:
    image: zabbix/zabbix-server-mysql:6.4-alpine-latest
    container_name: zabbix-server
    restart: unless-stopped
    environment:
      DB_SERVER_HOST: mysql-server
      MYSQL_DATABASE: zabbix
      MYSQL_USER: zabbix
      MYSQL_PASSWORD: zabbix_pwd
      MYSQL_ROOT_PASSWORD: root_pwd
      ZBX_JAVAGATEWAY: zabbix-java-gateway
      ZBX_JAVAGATEWAY_ENABLE: "true"
      ZBX_JAVAGATEWAYPORT: 10052
    depends_on:
      - mysql-server
    ports:
      - "10051:10051"
    volumes:
      - zabbix_server_data:/var/lib/zabbix
      - ./conf/zabbix_server.conf:/etc/zabbix/zabbix_server.conf:ro
    networks:
      - zabbix-net

  # Zabbix Web Interface
  zabbix-web:
    image: zabbix/zabbix-web-nginx-mysql:6.4-alpine-latest
    container_name: zabbix-web
    restart: unless-stopped
    environment:
      ZBX_SERVER_HOST: zabbix-server
      DB_SERVER_HOST: mysql-server
      MYSQL_DATABASE: zabbix
      MYSQL_USER: zabbix
      MYSQL_PASSWORD: zabbix_pwd
      PHP_TZ: Asia/Shanghai
    depends_on:
      - mysql-server
      - zabbix-server
    ports:
      - "8080:8080"
    volumes:
      - ./conf/nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - zabbix-net

  # Zabbix Agent
  zabbix-agent:
    image: zabbix/zabbix-agent:6.4-alpine-latest
    container_name: zabbix-agent
    restart: unless-stopped
    environment:
      ZBX_HOSTNAME: "Zabbix server"
      ZBX_SERVER_HOST: zabbix-server
      ZBX_SERVER_PORT: 10051
      ZBX_PASSIVE_ALLOW: "true"
      ZBX_ACTIVE_ALLOW: "true"
    depends_on:
      - zabbix-server
    ports:
      - "10050:10050"
    volumes:
      - ./conf/zabbix_agentd.conf:/etc/zabbix/zabbix_agentd.conf:ro
    networks:
      - zabbix-net

  # Java Gateway (for JMX monitoring)
  zabbix-java-gateway:
    image: zabbix/zabbix-java-gateway:6.4-alpine-latest
    container_name: zabbix-java-gateway
    restart: unless-stopped
    environment:
      ZBX_START_POLLERS: 5
    ports:
      - "10052:10052"
    networks:
      - zabbix-net

volumes:
  mysql_data:
  zabbix_server_data:

networks:
  zabbix-net:
    driver: bridge
