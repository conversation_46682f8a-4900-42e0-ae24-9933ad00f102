#!/bin/bash

# Zabbix部署脚本
# 用于快速部署Zabbix监控系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_prerequisites() {
    log_info "检查系统依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_info "系统依赖检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs
    mkdir -p data/mysql
    mkdir -p data/zabbix
    
    log_info "目录创建完成"
}

# 设置权限
set_permissions() {
    log_info "设置文件权限..."
    
    chmod +x scripts/*.sh
    chmod 644 conf/*.conf
    
    log_info "权限设置完成"
}

# 启动服务
start_services() {
    log_info "启动Zabbix服务..."
    
    # 拉取最新镜像
    docker-compose pull
    
    # 启动服务
    docker-compose up -d
    
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    docker-compose ps
    
    log_info "Zabbix服务启动完成"
}

# 显示访问信息
show_access_info() {
    log_info "Zabbix部署完成！"
    echo ""
    echo "访问信息："
    echo "  Web界面: http://localhost:8080"
    echo "  默认用户名: Admin"
    echo "  默认密码: zabbix"
    echo ""
    echo "服务端口："
    echo "  Zabbix Server: 10051"
    echo "  Zabbix Agent: 10050"
    echo "  Java Gateway: 10052"
    echo "  Web界面: 8080"
    echo ""
    echo "管理命令："
    echo "  查看日志: docker-compose logs -f"
    echo "  停止服务: docker-compose down"
    echo "  重启服务: docker-compose restart"
}

# 主函数
main() {
    log_info "开始部署Zabbix监控系统..."
    
    check_prerequisites
    create_directories
    set_permissions
    start_services
    show_access_info
    
    log_info "部署完成！"
}

# 执行主函数
main "$@"
