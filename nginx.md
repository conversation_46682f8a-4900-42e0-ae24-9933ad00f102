# Nginx 安装与 Syft 组件扫描记录

## 1. Nginx 安装过程

### 1.1 配置nginx官方仓库
```bash
# 安装yum-utils
yum install yum-utils -y

# 创建nginx官方仓库配置文件
cat > /etc/yum.repos.d/nginx.repo << 'EOF'
[nginx-stable]
name=nginx stable repo
baseurl=http://nginx.org/packages/centos/8/$basearch/
gpgcheck=1
enabled=1
gpgkey=https://nginx.org/keys/nginx_signing.key
module_hotfixes=true

[nginx-mainline]
name=nginx mainline repo
baseurl=http://nginx.org/packages/mainline/centos/8/$basearch/
gpgcheck=1
enabled=0
gpgkey=https://nginx.org/keys/nginx_signing.key
module_hotfixes=true
EOF
```

### 1.2 安装nginx官方预编译版本
```bash
# 清理缓存并安装nginx
yum clean all && yum install nginx -y
```

### 1.3 查看nginx编译模块信息
```bash
# 使用-V参数查看nginx编译时包含的模块
nginx -V
```

**输出结果：**
```
nginx version: nginx/1.28.0
built by gcc 8.5.0 20210514 (Red Hat 8.5.0-26) (GCC) 
built with OpenSSL 1.1.1k  FIPS 25 Mar 2021
TLS SNI support enabled
configure arguments: --prefix=/etc/nginx --sbin-path=/usr/sbin/nginx --modules-path=/usr/lib64/nginx/modules --conf-path=/etc/nginx/nginx.conf --error-log-path=/var/log/nginx/error.log --http-log-path=/var/log/nginx/access.log --pid-path=/var/run/nginx.pid --lock-path=/var/run/nginx.lock --http-client-body-temp-path=/var/cache/nginx/client_temp --http-proxy-temp-path=/var/cache/nginx/proxy_temp --http-fastcgi-temp-path=/var/cache/nginx/fastcgi_temp --http-uwsgi-temp-path=/var/cache/nginx/uwsgi_temp --http-scgi-temp-path=/var/cache/nginx/scgi_temp --user=nginx --group=nginx --with-compat --with-file-aio --with-threads --with-http_addition_module --with-http_auth_request_module --with-http_dav_module --with-http_flv_module --with-http_gunzip_module --with-http_gzip_static_module --with-http_mp4_module --with-http_random_index_module --with-http_realip_module --with-http_secure_link_module --with-http_slice_module --with-http_ssl_module --with-http_stub_status_module --with-http_sub_module --with-http_v2_module --with-http_v3_module --with-mail --with-mail_ssl_module --with-stream --with-stream_realip_module --with-stream_ssl_module --with-stream_ssl_preread_module --with-cc-opt='-O2 -g -pipe -Wall -Werror=format-security -Wp,-D_FORTIFY_SOURCE=2 -Wp,-D_GLIBCXX_ASSERTIONS -fexceptions -fstack-protector-strong -grecord-gcc-switches -specs=/usr/lib/rpm/redhat/redhat-hardened-cc1 -specs=/usr/lib/rpm/redhat/redhat-annobin-cc1 -m64 -mtune=generic -fasynchronous-unwind-tables -fstack-clash-protection -fcf-protection -fPIC' --with-ld-opt='-Wl,-z,relro -Wl,-z,now -pie'
```

### 1.4 启动nginx服务
```bash
# 启动nginx
systemctl start nginx

# 检查服务状态
systemctl status nginx

# 设置开机自启动
systemctl enable nginx

# 验证nginx运行状态
netstat -tlnp | grep nginx
curl -I localhost
```

## 2. Syft 安装过程

### 2.1 下载Syft（使用代理）
```bash
# 设置代理并下载Syft
export http_proxy=http://127.0.0.1:10808 && export https_proxy=http://127.0.0.1:10808 && wget https://github.com/anchore/syft/releases/download/v1.31.0/syft_1.31.0_linux_amd64.tar.gz
```

### 2.2 安装Syft
```bash
# 解压文件
tar -xzf syft_1.31.0_linux_amd64.tar.gz

# 移动到系统路径
sudo mv syft /usr/local/bin/ && chmod +x /usr/local/bin/syft

# 验证安装
syft version
```

**Syft版本信息：**
```
Application:   syft
Version:       1.31.0
BuildDate:     2025-08-13T14:50:51Z
GitCommit:     ab9db0024ed35ab6a4e33e539593f5a3c58a5594
GitDescription: v1.31.0
Platform:      linux/amd64
GoVersion:     go1.24.5
Compiler:      gc
SchemaVersion: 16.0.37
```

## 3. Syft 对 Nginx 组件扫描

### 3.1 查找nginx二进制文件位置
```bash
which nginx
# 输出: /usr/sbin/nginx
```

### 3.2 使用Syft扫描nginx二进制文件

#### 3.2.1 JSON格式输出（详细信息）
```bash
syft /usr/sbin/nginx -o syft-json
```

#### 3.2.2 表格格式输出（简洁信息）
```bash
syft /usr/sbin/nginx -o syft-table
```

**扫描结果：**
```
NAME   VERSION  TYPE      
nginx  1.28.0   binary  
```

#### 3.2.3 扫描nginx配置目录
```bash
syft /etc/nginx -o syft-table
# 结果: No packages discovered
```

## 4. 扫描结果分析

### 4.1 发现的组件
- **组件名称**: nginx
- **版本**: 1.28.0
- **类型**: binary
- **CPE标识符**: 
  - cpe:2.3:a:f5:nginx:1.28.0:*:*:*:*:*:*:*
  - cpe:2.3:a:nginx:nginx:1.28.0:*:*:*:*:*:*:*
- **PURL**: pkg:generic/nginx@1.28.0

### 4.2 二进制文件信息
- **文件路径**: /usr/sbin/nginx
- **文件大小**: 1,632,680 bytes
- **SHA256**: 620140acd46b871603aaf55f076e14a87c1d3d581b5c7ebb14f722b2e8748aa1
- **格式**: ELF
- **导入的库**:
  - libdl.so.2
  - libpthread.so.0
  - libcrypt.so.1
  - libpcre2-8.so.0
  - libssl.so.1.1
  - libcrypto.so.1.1
  - libz.so.1
  - libc.so.6

### 4.3 安全特性
- **符号表已剥离**: 是
- **栈保护**: 是
- **NX位**: 是
- **RELRO**: 完全
- **PIE**: 是
- **DSO**: 是
- **安全栈**: 否

## 5. 总结

通过Syft扫描，我们成功识别了nginx 1.28.0二进制文件及其依赖的系统库。扫描结果显示nginx是一个经过安全加固的二进制文件，启用了多种安全特性如栈保护、NX位、RELRO等。

这次扫描为后续的漏洞分析和安全评估提供了基础的软件清单信息。
